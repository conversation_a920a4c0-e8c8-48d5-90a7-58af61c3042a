from llama_index.readers.google import GoogleDriveReader
from google.oauth2 import service_account

# Path to your downloaded JSON key file
SERVICE_ACCOUNT_FILE = 'path/to/service_account.json'

# Define the scopes your app needs
SCOPES = ['https://www.googleapis.com/auth/drive.readonly']

# Create credentials object from the service account file
credentials = service_account.Credentials.from_service_account_file(
    SERVICE_ACCOUNT_FILE,
    scopes=SCOPES
)

# Initialize the reader with the credentials
reader = GoogleDriveReader(service_account_credentials=credentials)

# Load files using file IDs or folder IDs
documents = reader.load_data(file_ids=["1a2b3c..."], recursive=True)
