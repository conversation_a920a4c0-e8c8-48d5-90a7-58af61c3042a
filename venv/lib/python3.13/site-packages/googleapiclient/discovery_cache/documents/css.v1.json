{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/content": {"description": "Manage your product listings and accounts for Google Shopping"}}}}, "basePath": "", "baseUrl": "https://css.googleapis.com/", "batchPath": "batch", "canonicalName": "Css", "description": "Programmatically manage your Comparison Shopping Service (CSS) account data at scale.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/comparison-shopping-services/api/overview", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "css:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://css.mtls.googleapis.com/", "name": "css", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accounts": {"methods": {"get": {"description": "Retrieves a single CSS/MC account by ID.", "flatPath": "v1/accounts/{accountsId}", "httpMethod": "GET", "id": "css.accounts.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the managed CSS/MC account. Format: accounts/{account}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Optional. Only required when retrieving MC account information. The CSS domain that is the parent resource of the MC account. Format: accounts/{account}", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Account"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "listChildAccounts": {"description": "Lists all the accounts under the specified CSS account ID, and optionally filters by label ID and account name.", "flatPath": "v1/accounts/{accountsId}:listChildAccounts", "httpMethod": "GET", "id": "css.accounts.listChildAccounts", "parameterOrder": ["parent"], "parameters": {"fullName": {"description": "If set, only the MC accounts with the given name (case sensitive) will be returned.", "location": "query", "type": "string"}, "labelId": {"description": "If set, only the MC accounts with the given label ID will be returned.", "format": "int64", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of accounts to return. The service may return fewer than this value. If unspecified, at most 50 accounts will be returned. The maximum value is 100; values above 100 will be coerced to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListChildAccounts` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListChildAccounts` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent account. Must be a CSS group or domain. Format: accounts/{account}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}:listChildAccounts", "response": {"$ref": "ListChildAccountsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "updateLabels": {"description": "Updates labels assigned to CSS/MC accounts by a CSS domain.", "flatPath": "v1/accounts/{accountsId}:updateLabels", "httpMethod": "POST", "id": "css.accounts.updateLabels", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The label resource name. Format: accounts/{account}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:updateLabels", "request": {"$ref": "UpdateAccountLabelsRequest"}, "response": {"$ref": "Account"}, "scopes": ["https://www.googleapis.com/auth/content"]}}, "resources": {"cssProductInputs": {"methods": {"delete": {"description": "Deletes a CSS Product input from your CSS Center account. After a delete it may take several minutes until the input is no longer available.", "flatPath": "v1/accounts/{accountsId}/cssProductInputs/{cssProductInputsId}", "httpMethod": "DELETE", "id": "css.accounts.cssProductInputs.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the CSS product input resource to delete. Format: accounts/{account}/cssProductInputs/{css_product_input}, where the last section `css_product_input` consists of 3 parts: contentLanguage~feedLabel~offerId. Example: accounts/123/cssProductInputs/de~DE~rawProvidedId123", "location": "path", "pattern": "^accounts/[^/]+/cssProductInputs/[^/]+$", "required": true, "type": "string"}, "supplementalFeedId": {"description": "The Content API Supplemental Feed ID. The field must not be set if the action applies to a primary feed. If the field is set, then product action applies to a supplemental feed instead of primary Content API feed.", "format": "int64", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "insert": {"description": "Uploads a CssProductInput to your CSS Center account. If an input with the same contentLanguage, identity, feedLabel and feedId already exists, this method replaces that entry. After inserting, updating, or deleting a CSS Product input, it may take several minutes before the processed CSS Product can be retrieved.", "flatPath": "v1/accounts/{accountsId}/cssProductInputs:insert", "httpMethod": "POST", "id": "css.accounts.cssProductInputs.insert", "parameterOrder": ["parent"], "parameters": {"feedId": {"deprecated": true, "description": "Optional. DEPRECATED. Feed id is not required for CSS Products. The primary or supplemental feed id. If CSS Product already exists and feed id provided is different, then the CSS Product will be moved to a new feed. Note: For now, CSSs do not need to provide feed ids as we create feeds on the fly. We do not have supplemental feed support for CSS Products yet.", "format": "int64", "location": "query", "type": "string"}, "parent": {"description": "Required. The account where this CSS Product will be inserted. Format: accounts/{account}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/cssProductInputs:insert", "request": {"$ref": "CssProductInput"}, "response": {"$ref": "CssProductInput"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "patch": {"description": "Updates the existing Css Product input in your CSS Center account. After inserting, updating, or deleting a CSS Product input, it may take several minutes before the processed Css Product can be retrieved.", "flatPath": "v1/accounts/{accountsId}/cssProductInputs/{cssProductInputsId}", "httpMethod": "PATCH", "id": "css.accounts.cssProductInputs.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The name of the CSS Product input. Format: `accounts/{account}/cssProductInputs/{css_product_input}`, where the last section `css_product_input` consists of 3 parts: contentLanguage~feedLabel~offerId. Example: accounts/123/cssProductInputs/de~DE~rawProvidedId123", "location": "path", "pattern": "^accounts/[^/]+/cssProductInputs/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The list of CSS product attributes to be updated. If the update mask is omitted, then it is treated as implied field mask equivalent to all fields that are populated (have a non-empty value). Attributes specified in the update mask without a value specified in the body will be deleted from the CSS product. Update mask can only be specified for top level fields in attributes and custom attributes. To specify the update mask for custom attributes you need to add the `custom_attribute.` prefix. Providing special \"*\" value for full CSS product replacement is not supported.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "CssProductInput"}, "response": {"$ref": "CssProductInput"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "cssProducts": {"methods": {"get": {"description": "Retrieves the processed CSS Product from your CSS Center account. After inserting, updating, or deleting a product input, it may take several minutes before the updated final product can be retrieved.", "flatPath": "v1/accounts/{accountsId}/cssProducts/{cssProductsId}", "httpMethod": "GET", "id": "css.accounts.cssProducts.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the CSS product to retrieve.", "location": "path", "pattern": "^accounts/[^/]+/cssProducts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "CssProduct"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists the processed CSS Products in your CSS Center account. The response might contain fewer items than specified by pageSize. Rely on pageToken to determine if there are more items to be requested. After inserting, updating, or deleting a CSS product input, it may take several minutes before the updated processed CSS product can be retrieved.", "flatPath": "v1/accounts/{accountsId}/cssProducts", "httpMethod": "GET", "id": "css.accounts.cssProducts.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of CSS Products to return. The service may return fewer than this value. The maximum value is 1000; values above 1000 will be coerced to 1000. If unspecified, the maximum number of CSS products will be returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListCssProducts` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListCssProducts` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The account/domain to list processed CSS Products for. Format: accounts/{account}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/cssProducts", "response": {"$ref": "ListCssProductsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "labels": {"methods": {"create": {"description": "Creates a new label, not assigned to any account.", "flatPath": "v1/accounts/{accountsId}/labels", "httpMethod": "POST", "id": "css.accounts.labels.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent account. Format: accounts/{account}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/labels", "request": {"$ref": "Account<PERSON><PERSON><PERSON>"}, "response": {"$ref": "Account<PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "delete": {"description": "Deletes a label and removes it from all accounts to which it was assigned.", "flatPath": "v1/accounts/{accountsId}/labels/{labelsId}", "httpMethod": "DELETE", "id": "css.accounts.labels.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the label to delete. Format: accounts/{account}/labels/{label}", "location": "path", "pattern": "^accounts/[^/]+/labels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists the labels owned by an account.", "flatPath": "v1/accounts/{accountsId}/labels", "httpMethod": "GET", "id": "css.accounts.labels.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of labels to return. The service may return fewer than this value. If unspecified, at most 50 labels will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListAccount<PERSON>abels` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListAccountLabels` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent account. Format: accounts/{account}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/labels", "response": {"$ref": "ListAccountLabelsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "patch": {"description": "Updates a label.", "flatPath": "v1/accounts/{accountsId}/labels/{labelsId}", "httpMethod": "PATCH", "id": "css.accounts.labels.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the label. Format: accounts/{account}/labels/{label}", "location": "path", "pattern": "^accounts/[^/]+/labels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Account<PERSON><PERSON><PERSON>"}, "response": {"$ref": "Account<PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "quotas": {"methods": {"list": {"description": "Lists the daily call quota and usage per group for your CSS Center account.", "flatPath": "v1/accounts/{accountsId}/quotas", "httpMethod": "GET", "id": "css.accounts.quotas.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of quotas to return in the response, used for paging. Defaults to 500; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Token (if provided) to retrieve the subsequent page. All other parameters must match the original call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The CSS account that owns the collection of method quotas and resources. In most cases, this is the CSS domain. Format: accounts/{account}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/quotas", "response": {"$ref": "ListQuotaGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}}}}, "revision": "********", "rootUrl": "https://css.googleapis.com/", "schemas": {"Account": {"description": "Information about CSS/MC account.", "id": "Account", "properties": {"accountType": {"description": "Output only. The type of this account.", "enum": ["ACCOUNT_TYPE_UNSPECIFIED", "CSS_GROUP", "CSS_DOMAIN", "MC_PRIMARY_CSS_MCA", "MC_CSS_MCA", "MC_MARKETPLACE_MCA", "MC_OTHER_MCA", "MC_STANDALONE", "MC_MCA_SUBACCOUNT"], "enumDescriptions": ["Unknown account type.", "CSS group account.", "CSS domain account.", "MC Primary CSS MCA account.", "MC CSS MCA account.", "MC Marketplace MCA account.", "MC Other MCA account.", "MC Standalone account.", "MC MCA sub-account."], "readOnly": true, "type": "string"}, "automaticLabelIds": {"description": "Automatically created label IDs assigned to the MC account by CSS Center.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "displayName": {"description": "The CSS/MC account's short display name.", "type": "string"}, "fullName": {"description": "Output only. Immutable. The CSS/MC account's full name.", "readOnly": true, "type": "string"}, "homepageUri": {"description": "Output only. Immutable. The CSS/MC account's homepage.", "readOnly": true, "type": "string"}, "labelIds": {"description": "Manually created label IDs assigned to the CSS/MC account by a CSS parent account.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "name": {"description": "The label resource name. Format: accounts/{account}", "type": "string"}, "parent": {"description": "The CSS/MC account's parent resource. CSS group for CSS domains; CSS domain for MC accounts. Returned only if the user has access to the parent account. Note: For MC sub-accounts, this is also the CSS domain that is the parent resource of the MCA account, since we are effectively flattening the hierarchy.\"", "type": "string"}}, "type": "object"}, "AccountLabel": {"description": "Label assigned by CSS domain or CSS group to one of its sub-accounts.", "id": "Account<PERSON><PERSON><PERSON>", "properties": {"accountId": {"description": "Output only. The ID of account this label belongs to.", "format": "int64", "readOnly": true, "type": "string"}, "description": {"description": "The description of this label.", "type": "string"}, "displayName": {"description": "The display name of this label.", "type": "string"}, "labelId": {"description": "Output only. The ID of the label.", "format": "int64", "readOnly": true, "type": "string"}, "labelType": {"description": "Output only. The type of this label.", "enum": ["LABEL_TYPE_UNSPECIFIED", "MANUAL", "AUTOMATIC"], "enumDescriptions": ["Unknown label type.", "Indicates that the label was created manually.", "Indicates that the label was created automatically by CSS Center."], "readOnly": true, "type": "string"}, "name": {"description": "Identifier. The resource name of the label. Format: accounts/{account}/labels/{label}", "type": "string"}}, "type": "object"}, "Attributes": {"description": "Attributes for CSS Product.", "id": "Attributes", "properties": {"additionalImageLinks": {"description": "Additional URL of images of the item.", "items": {"type": "string"}, "type": "array"}, "adult": {"description": "Set to true if the item is targeted towards adults.", "type": "boolean"}, "ageGroup": {"description": "Target age group of the item.", "type": "string"}, "brand": {"description": "Product Related Attributes.[14-36] Brand of the item.", "type": "string"}, "certifications": {"description": "A list of certificates claimed by the CSS for the given product.", "items": {"$ref": "Certification"}, "type": "array"}, "color": {"description": "Color of the item.", "type": "string"}, "cppAdsRedirect": {"description": "Allows advertisers to override the item URL when the product is shown within the context of Product Ads.", "type": "string"}, "cppLink": {"description": "URL directly linking to your the Product Detail Page of the CSS.", "type": "string"}, "cppMobileLink": {"description": "URL for the mobile-optimized version of the Product Detail Page of the CSS.", "type": "string"}, "customLabel0": {"description": "Custom label 0 for custom grouping of items in a Shopping campaign.", "type": "string"}, "customLabel1": {"description": "Custom label 1 for custom grouping of items in a Shopping campaign.", "type": "string"}, "customLabel2": {"description": "Custom label 2 for custom grouping of items in a Shopping campaign.", "type": "string"}, "customLabel3": {"description": "Custom label 3 for custom grouping of items in a Shopping campaign.", "type": "string"}, "customLabel4": {"description": "Custom label 4 for custom grouping of items in a Shopping campaign.", "type": "string"}, "description": {"description": "Description of the item.", "type": "string"}, "excludedDestinations": {"description": "The list of destinations to exclude for this target (corresponds to unchecked check boxes in Merchant Center).", "items": {"type": "string"}, "type": "array"}, "expirationDate": {"description": "Date on which the item should expire, as specified upon insertion, in [ISO 8601](http://en.wikipedia.org/wiki/ISO_8601) format. The actual expiration date is exposed in `productstatuses` as [googleExpirationDate](https://support.google.com/merchants/answer/6324499) and might be earlier if `expirationDate` is too far in the future. Note: It may take 2+ days from the expiration date for the item to actually get deleted.", "format": "google-datetime", "type": "string"}, "gender": {"description": "Target gender of the item.", "type": "string"}, "googleProductCategory": {"description": "Google's category of the item (see [Google product taxonomy](https://support.google.com/merchants/answer/1705911)). When querying products, this field will contain the user provided value. There is currently no way to get back the auto assigned google product categories through the API.", "type": "string"}, "gtin": {"description": "Global Trade Item Number ([GTIN](https://support.google.com/merchants/answer/188494#gtin)) of the item.", "type": "string"}, "headlineOfferCondition": {"description": "Condition of the headline offer.", "type": "string"}, "headlineOfferInstallment": {"$ref": "HeadlineOfferInstallment", "description": "Number and amount of installments to pay for an item."}, "headlineOfferLink": {"description": "Link to the headline offer.", "type": "string"}, "headlineOfferMobileLink": {"description": "Mobile Link to the headline offer.", "type": "string"}, "headlineOfferPrice": {"$ref": "Price", "description": "Headline Price of the CSS Product."}, "headlineOfferShippingPrice": {"$ref": "Price", "description": "Headline Price of the CSS Product."}, "headlineOfferSubscriptionCost": {"$ref": "HeadlineOfferSubscriptionCost", "description": "Number of periods (months or years) and amount of payment per period for an item with an associated subscription contract."}, "highPrice": {"$ref": "Price", "description": "High Price of the CSS Product."}, "imageLink": {"description": "URL of an image of the item.", "type": "string"}, "includedDestinations": {"description": "The list of destinations to include for this target (corresponds to checked check boxes in Merchant Center). Default destinations are always included unless provided in `excludedDestinations`.", "items": {"type": "string"}, "type": "array"}, "isBundle": {"description": "Whether the item is a merchant-defined bundle. A bundle is a custom grouping of different products sold by a merchant for a single price.", "type": "boolean"}, "itemGroupId": {"description": "Shared identifier for all variants of the same product.", "type": "string"}, "lowPrice": {"$ref": "Price", "description": "Low Price of the CSS Product."}, "material": {"description": "The material of which the item is made.", "type": "string"}, "mpn": {"description": "Manufacturer Part Number ([MPN](https://support.google.com/merchants/answer/188494#mpn)) of the item.", "type": "string"}, "multipack": {"description": "The number of identical products in a merchant-defined multipack.", "format": "int64", "type": "string"}, "numberOfOffers": {"description": "The number of CSS Products.", "format": "int64", "type": "string"}, "pattern": {"description": "The item's pattern (e.g. polka dots).", "type": "string"}, "pause": {"description": "Publication of this item will be temporarily paused.", "type": "string"}, "productDetails": {"description": "Technical specification or additional product details.", "items": {"$ref": "ProductDetail"}, "type": "array"}, "productHeight": {"$ref": "ProductDimension", "description": "The height of the product in the units provided. The value must be between 0 (exclusive) and 3000 (inclusive)."}, "productHighlights": {"description": "Bullet points describing the most relevant highlights of a product.", "items": {"type": "string"}, "type": "array"}, "productLength": {"$ref": "ProductDimension", "description": "The length of the product in the units provided. The value must be between 0 (exclusive) and 3000 (inclusive)."}, "productTypes": {"description": "Categories of the item (formatted as in [products data specification](https://support.google.com/merchants/answer/6324406)).", "items": {"type": "string"}, "type": "array"}, "productWeight": {"$ref": "ProductWeight", "description": "The weight of the product in the units provided. The value must be between 0 (exclusive) and 2000 (inclusive)."}, "productWidth": {"$ref": "ProductDimension", "description": "The width of the product in the units provided. The value must be between 0 (exclusive) and 3000 (inclusive)."}, "size": {"description": "Size of the item. Only one value is allowed. For variants with different sizes, insert a separate product for each size with the same `itemGroupId` value (see [https://support.google.com/merchants/answer/6324492](size definition)).", "type": "string"}, "sizeSystem": {"description": "System in which the size is specified. Recommended for apparel items.", "type": "string"}, "sizeTypes": {"description": "The cut of the item. It can be used to represent combined size types for apparel items. Maximum two of size types can be provided (see [https://support.google.com/merchants/answer/6324497](size type)).", "items": {"type": "string"}, "type": "array"}, "title": {"description": "Title of the item.", "type": "string"}}, "type": "object"}, "Certification": {"description": "The certification for the product. Use the this attribute to describe certifications, such as energy efficiency ratings, associated with a product.", "id": "Certification", "properties": {"authority": {"description": "The authority or certification body responsible for issuing the certification. At this time, the most common value is \"EC\" or “European_Commission” for energy labels in the EU.", "type": "string"}, "code": {"description": "The code of the certification. For example, for the EPREL certificate with the link https://eprel.ec.europa.eu/screen/product/dishwashers2019/123456 the code is 123456. The code is required for European Energy Labels.", "type": "string"}, "name": {"description": "The name of the certification. At this time, the most common value is \"EPREL\", which represents energy efficiency certifications in the EU European Registry for Energy Labeling (EPREL) database.", "type": "string"}}, "type": "object"}, "CssProduct": {"description": "The processed CSS Product.", "id": "CssProduct", "properties": {"attributes": {"$ref": "Attributes", "description": "Output only. A list of product attributes.", "readOnly": true}, "contentLanguage": {"description": "Output only. The two-letter [ISO 639-1](http://en.wikipedia.org/wiki/ISO_639-1) language code for the product.", "readOnly": true, "type": "string"}, "cssProductStatus": {"$ref": "CssProductStatus", "description": "Output only. The status of a product, data validation issues, that is, information about a product computed asynchronously.", "readOnly": true}, "customAttributes": {"description": "Output only. A list of custom (CSS-provided) attributes. It can also be used to submit any attribute of the feed specification in its generic form (for example, `{ \"name\": \"size type\", \"value\": \"regular\" }`). This is useful for submitting attributes not explicitly exposed by the API, such as additional attributes used for Buy on Google.", "items": {"$ref": "CustomAttribute"}, "readOnly": true, "type": "array"}, "feedLabel": {"description": "Output only. The feed label for the product.", "readOnly": true, "type": "string"}, "name": {"description": "The name of the CSS Product. Format: `\"accounts/{account}/cssProducts/{css_product}\"`", "type": "string"}, "rawProvidedId": {"description": "Output only. Your unique raw identifier for the product.", "readOnly": true, "type": "string"}}, "type": "object"}, "CssProductInput": {"description": "This resource represents input data you submit for a CSS Product, not the processed CSS Product that you see in CSS Center, in Shopping Ads, or across Google surfaces.", "id": "CssProductInput", "properties": {"attributes": {"$ref": "Attributes", "description": "A list of CSS Product attributes."}, "contentLanguage": {"description": "Required. The two-letter [ISO 639-1](http://en.wikipedia.org/wiki/ISO_639-1) language code for the CSS Product.", "type": "string"}, "customAttributes": {"description": "A list of custom (CSS-provided) attributes. It can also be used for submitting any attribute of the feed specification in its generic form (for example: `{ \"name\": \"size type\", \"value\": \"regular\" }`). This is useful for submitting attributes not explicitly exposed by the API, such as additional attributes used for Buy on Google.", "items": {"$ref": "CustomAttribute"}, "type": "array"}, "feedLabel": {"description": "Required. The [feed label](https://developers.google.com/shopping-content/guides/products/feed-labels) for the CSS Product. Feed Label is synonymous to \"target country\" and hence should always be a valid region code. For example: 'DE' for Germany, 'FR' for France.", "type": "string"}, "finalName": {"description": "Output only. The name of the processed CSS Product. Format: `accounts/{account}/cssProducts/{css_product}` \"", "readOnly": true, "type": "string"}, "freshnessTime": {"deprecated": true, "description": "DEPRECATED. Use expiration_date instead. Represents the existing version (freshness) of the CSS Product, which can be used to preserve the right order when multiple updates are done at the same time. This field must not be set to the future time. If set, the update is prevented if a newer version of the item already exists in our system (that is the last update time of the existing CSS products is later than the freshness time set in the update). If the update happens, the last update time is then set to this freshness time. If not set, the update will not be prevented and the last update time will default to when this request was received by the CSS API. If the operation is prevented, the aborted exception will be thrown.", "format": "google-datetime", "type": "string"}, "name": {"description": "Identifier. The name of the CSS Product input. Format: `accounts/{account}/cssProductInputs/{css_product_input}`, where the last section `css_product_input` consists of 3 parts: contentLanguage~feedLabel~offerId. Example: accounts/123/cssProductInputs/de~DE~rawProvidedId123", "type": "string"}, "rawProvidedId": {"description": "Required. Your unique identifier for the CSS Product. This is the same for the CSS Product input and processed CSS Product. We only allow ids with alphanumerics, underscores and dashes. See the [products feed specification](https://support.google.com/merchants/answer/188494#id) for details.", "type": "string"}}, "type": "object"}, "CssProductStatus": {"description": "The status of the Css Product, data validation issues, that is, information about the Css Product computed asynchronously.", "id": "CssProductStatus", "properties": {"creationDate": {"description": "Date on which the item has been created, in [ISO 8601](http://en.wikipedia.org/wiki/ISO_8601) format.", "format": "google-datetime", "type": "string"}, "destinationStatuses": {"description": "The intended destinations for the product.", "items": {"$ref": "DestinationStatus"}, "type": "array"}, "googleExpirationDate": {"description": "Date on which the item expires, in [ISO 8601](http://en.wikipedia.org/wiki/ISO_8601) format.", "format": "google-datetime", "type": "string"}, "itemLevelIssues": {"description": "A list of all issues associated with the product.", "items": {"$ref": "ItemLevelIssue"}, "type": "array"}, "lastUpdateDate": {"description": "Date on which the item has been last updated, in [ISO 8601](http://en.wikipedia.org/wiki/ISO_8601) format.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "CustomAttribute": {"description": "A message that represents custom attributes. Exactly one of `value` or `group_values` must not be empty.", "id": "CustomAttribute", "properties": {"groupValues": {"description": "Subattributes within this attribute group. If `group_values` is not empty, `value` must be empty.", "items": {"$ref": "CustomAttribute"}, "type": "array"}, "name": {"description": "The name of the attribute.", "type": "string"}, "value": {"description": "The value of the attribute. If `value` is not empty, `group_values` must be empty.", "type": "string"}}, "type": "object"}, "DestinationStatus": {"description": "The destination status of the product status.", "id": "DestinationStatus", "properties": {"approvedCountries": {"description": "List of country codes (ISO 3166-1 alpha-2) where the CSS Product is approved.", "items": {"type": "string"}, "type": "array"}, "destination": {"description": "The name of the destination", "type": "string"}, "disapprovedCountries": {"description": "List of country codes (ISO 3166-1 alpha-2) where the CSS Product is disapproved.", "items": {"type": "string"}, "type": "array"}, "pendingCountries": {"description": "List of country codes (ISO 3166-1 alpha-2) where the CSS Product is pending approval.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "HeadlineOfferInstallment": {"description": "A message that represents installment.", "id": "HeadlineOfferInstallment", "properties": {"amount": {"$ref": "Price", "description": "The amount the buyer has to pay per month."}, "downpayment": {"$ref": "Price", "description": "The up-front down payment amount the buyer has to pay."}, "months": {"description": "The number of installments the buyer has to pay.", "format": "int64", "type": "string"}}, "type": "object"}, "HeadlineOfferSubscriptionCost": {"description": "The SubscriptionCost of the product.", "id": "HeadlineOfferSubscriptionCost", "properties": {"amount": {"$ref": "Price", "description": "The amount the buyer has to pay per subscription period."}, "period": {"description": "The type of subscription period. Supported values are: * \"`month`\" * \"`year`\"", "enum": ["SUBSCRIPTION_PERIOD_UNSPECIFIED", "MONTH", "YEAR"], "enumDescriptions": ["Indicates that the subscription period is unspecified.", "Indicates that the subscription period is month.", "Indicates that the subscription period is year."], "type": "string"}, "periodLength": {"description": "The number of subscription periods the buyer has to pay.", "format": "int64", "type": "string"}}, "type": "object"}, "ItemLevelIssue": {"description": "The ItemLevelIssue of the product status.", "id": "ItemLevelIssue", "properties": {"applicableCountries": {"description": "List of country codes (ISO 3166-1 alpha-2) where issue applies to the CSS Product.", "items": {"type": "string"}, "type": "array"}, "attribute": {"description": "The attribute's name, if the issue is caused by a single attribute.", "type": "string"}, "code": {"description": "The error code of the issue.", "type": "string"}, "description": {"description": "A short issue description in English.", "type": "string"}, "destination": {"description": "The destination the issue applies to.", "type": "string"}, "detail": {"description": "A detailed issue description in English.", "type": "string"}, "documentation": {"description": "The URL of a web page to help with resolving this issue.", "type": "string"}, "resolution": {"description": "Whether the issue can be resolved by the merchant.", "type": "string"}, "servability": {"description": "How this issue affects serving of the CSS Product.", "type": "string"}}, "type": "object"}, "ListAccountLabelsResponse": {"description": "Response message for the `ListAccountLabels` method.", "id": "ListAccountLabelsResponse", "properties": {"accountLabels": {"description": "The labels from the specified account.", "items": {"$ref": "Account<PERSON><PERSON><PERSON>"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListChildAccountsResponse": {"description": "Response message for the `ListChildAccounts` method.", "id": "ListChildAccountsResponse", "properties": {"accounts": {"description": "The CSS/MC accounts returned for the specified CSS parent account.", "items": {"$ref": "Account"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListCssProductsResponse": {"description": "Response message for the ListCssProducts method.", "id": "ListCssProductsResponse", "properties": {"cssProducts": {"description": "The processed CSS products from the specified account. These are your processed CSS products after applying rules and supplemental feeds.", "items": {"$ref": "CssProduct"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListQuotaGroupsResponse": {"description": "Response message for the ListMethodGroups method.", "id": "ListQuotaGroupsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "quotaGroups": {"description": "The methods, current quota usage and limits per each group. The quota is shared between all methods in the group. The groups are sorted in descending order based on quota_usage.", "items": {"$ref": "QuotaGroup"}, "type": "array"}}, "type": "object"}, "MethodDetails": {"description": "The method details per method in the CSS API.", "id": "MethodDetails", "properties": {"method": {"description": "Output only. The name of the method for example `cssproductsservice.listcssproducts`.", "readOnly": true, "type": "string"}, "path": {"description": "Output only. The path for the method such as `v1/cssproductsservice.listcssproducts`.", "readOnly": true, "type": "string"}, "subapi": {"description": "Output only. The sub-API that the method belongs to. In the CSS API, this is always `css`.", "readOnly": true, "type": "string"}, "version": {"description": "Output only. The API version that the method belongs to.", "readOnly": true, "type": "string"}}, "type": "object"}, "Price": {"description": "The price represented as a number and currency.", "id": "Price", "properties": {"amountMicros": {"description": "The price represented as a number in micros (1 million micros is an equivalent to one's currency standard unit, for example, 1 USD = 1000000 micros).", "format": "int64", "type": "string"}, "currencyCode": {"description": "The currency of the price using three-letter acronyms according to [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217).", "type": "string"}}, "type": "object"}, "ProductDetail": {"description": "The product details.", "id": "ProductDetail", "properties": {"attributeName": {"description": "The name of the product detail.", "type": "string"}, "attributeValue": {"description": "The value of the product detail.", "type": "string"}, "sectionName": {"description": "The section header used to group a set of product details.", "type": "string"}}, "type": "object"}, "ProductDimension": {"description": "The dimension of the product.", "id": "ProductDimension", "properties": {"unit": {"description": "Required. The dimension units. Acceptable values are: * \"`in`\" * \"`cm`\"", "type": "string"}, "value": {"description": "Required. The dimension value represented as a number. The value can have a maximum precision of four decimal places.", "format": "double", "type": "number"}}, "type": "object"}, "ProductWeight": {"description": "The weight of the product.", "id": "ProductWeight", "properties": {"unit": {"description": "Required. The weight unit. Acceptable values are: * \"`g`\" * \"`kg`\" * \"`oz`\" * \"`lb`\"", "type": "string"}, "value": {"description": "Required. The weight represented as a number. The weight can have a maximum precision of four decimal places.", "format": "double", "type": "number"}}, "type": "object"}, "QuotaGroup": {"description": "The group information for methods in the CSS API. The quota is shared between all methods in the group. Even if none of the methods within the group have usage the information for the group is returned.", "id": "QuotaGroup", "properties": {"methodDetails": {"description": "Output only. List of all methods group quota applies to.", "items": {"$ref": "MethodDetails"}, "readOnly": true, "type": "array"}, "name": {"description": "Identifier. The resource name of the quota group. Format: accounts/{account}/quotas/{group} Example: `accounts/********/quotas/css-products-insert` Note: The {group} part is not guaranteed to follow a specific pattern.", "type": "string"}, "quotaLimit": {"description": "Output only. The maximum number of calls allowed per day for the group.", "format": "int64", "readOnly": true, "type": "string"}, "quotaMinuteLimit": {"description": "Output only. The maximum number of calls allowed per minute for the group.", "format": "int64", "readOnly": true, "type": "string"}, "quotaUsage": {"description": "Output only. The current quota usage, meaning the number of calls already made on a given day to the methods in the group. The daily quota limits reset at at 12:00 PM midday UTC.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "UpdateAccountLabelsRequest": {"description": "The request message for the `UpdateLabels` method.", "id": "UpdateAccountLabelsRequest", "properties": {"labelIds": {"description": "The list of label IDs to overwrite the existing account label IDs. If the list is empty, all currently assigned label IDs will be deleted.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "parent": {"description": "Optional. Only required when updating MC account labels. The CSS domain that is the parent resource of the MC account. Format: accounts/{account}", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "CSS API", "version": "v1", "version_module": true}