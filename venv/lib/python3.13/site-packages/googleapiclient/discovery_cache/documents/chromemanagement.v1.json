{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/chrome.management.appdetails.readonly": {"description": "See detailed information about apps installed on Chrome browsers and devices managed by your organization"}, "https://www.googleapis.com/auth/chrome.management.profiles": {"description": "See, edit, delete, and take other necessary actions on Chrome browser profiles managed by your organization"}, "https://www.googleapis.com/auth/chrome.management.profiles.readonly": {"description": "See Chrome browser profiles managed by your organization"}, "https://www.googleapis.com/auth/chrome.management.reports.readonly": {"description": "See reports about devices and Chrome browsers managed within your organization"}, "https://www.googleapis.com/auth/chrome.management.telemetry.readonly": {"description": "See basic device and telemetry information collected from ChromeOS devices or users managed within your organization"}}}}, "basePath": "", "baseUrl": "https://chromemanagement.googleapis.com/", "batchPath": "batch", "canonicalName": "Chrome Management", "description": "The Chrome Management API is a suite of services that allows Chrome administrators to view, manage and gain insights on their Chrome OS and Chrome Browser devices.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/chrome/management/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "chromemanagement:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://chromemanagement.mtls.googleapis.com/", "name": "chromemanagement", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"customers": {"resources": {"apps": {"methods": {"countChromeAppRequests": {"description": "Generate summary of app installation requests.", "flatPath": "v1/customers/{customersId}/apps:countChromeAppRequests", "httpMethod": "GET", "id": "chromemanagement.customers.apps.countChromeAppRequests", "parameterOrder": ["customer"], "parameters": {"customer": {"description": "Required. Customer id or \"my_customer\" to use the customer associated to the account making the request.", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}, "orderBy": {"description": "Field used to order results. Supported fields: * request_count * latest_request_time", "location": "query", "type": "string"}, "orgUnitId": {"description": "The ID of the organizational unit.", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of results to return. Maximum and default are 50, anything above will be coerced to 50.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token to specify the page of the request to be returned.", "location": "query", "type": "string"}}, "path": "v1/{+customer}/apps:countChromeAppRequests", "response": {"$ref": "GoogleChromeManagementV1CountChromeAppRequestsResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.appdetails.readonly"]}, "fetchDevicesRequestingExtension": {"description": "Get a list of devices that have requested to install an extension.", "flatPath": "v1/customers/{customersId}/apps:fetchDevicesRequestingExtension", "httpMethod": "GET", "id": "chromemanagement.customers.apps.fetchDevicesRequestingExtension", "parameterOrder": ["customer"], "parameters": {"customer": {"description": "Required. The customer ID or \"my_customer\" prefixed with \"customers/\".", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}, "extensionId": {"description": "Required. The extension for which we want to find requesting devices.", "location": "query", "type": "string"}, "orgUnitId": {"description": "The ID of the organizational unit. Only consider devices that directly belong to this org unit, i.e. sub-orgunits are not counted. If omitted, all data will be returned.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Maximum number of results to return. Maximum and default are 50. Any page size larger than 50 will be coerced to 50.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Token to specify the page of the request to be returned. Token expires after 1 day.", "location": "query", "type": "string"}}, "path": "v1/{+customer}/apps:fetchDevicesRequestingExtension", "response": {"$ref": "GoogleChromeManagementV1FetchDevicesRequestingExtensionResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.appdetails.readonly"]}, "fetchUsersRequestingExtension": {"description": "Get a list of users that have requested to install an extension.", "flatPath": "v1/customers/{customersId}/apps:fetchUsersRequestingExtension", "httpMethod": "GET", "id": "chromemanagement.customers.apps.fetchUsersRequestingExtension", "parameterOrder": ["customer"], "parameters": {"customer": {"description": "Required. The customer ID or \"my_customer\" prefixed with \"customers/\".", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}, "extensionId": {"description": "Required. The extension for which we want to find the requesting users.", "location": "query", "type": "string"}, "orgUnitId": {"description": "The ID of the organizational unit. Only consider devices that directly belong to this org unit, i.e. sub-orgunits are not counted. If omitted, all data will be returned.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Maximum number of results to return. Maximum and default are 50. Any page size larger than 50 will be coerced to 50.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Token to specify the page of the request to be returned. Token expires after 1 day.", "location": "query", "type": "string"}}, "path": "v1/{+customer}/apps:fetchUsersRequestingExtension", "response": {"$ref": "GoogleChromeManagementV1FetchUsersRequestingExtensionResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.appdetails.readonly"]}}, "resources": {"android": {"methods": {"get": {"description": "Get a specific app for a customer by its resource name.", "flatPath": "v1/customers/{customersId}/apps/android/{androidId}", "httpMethod": "GET", "id": "chromemanagement.customers.apps.android.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The app for which details are being queried. Examples: \"customers/my_customer/apps/chrome/gmbmikajjgmnabiglmofipeabaddhgne@2.1.2\" for the Save to Google Drive Chrome extension version 2.1.2, \"customers/my_customer/apps/android/com.google.android.apps.docs\" for the Google Drive Android app's latest version.", "location": "path", "pattern": "^customers/[^/]+/apps/android/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleChromeManagementV1AppDetails"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.appdetails.readonly"]}}}, "chrome": {"methods": {"get": {"description": "Get a specific app for a customer by its resource name.", "flatPath": "v1/customers/{customersId}/apps/chrome/{chromeId}", "httpMethod": "GET", "id": "chromemanagement.customers.apps.chrome.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The app for which details are being queried. Examples: \"customers/my_customer/apps/chrome/gmbmikajjgmnabiglmofipeabaddhgne@2.1.2\" for the Save to Google Drive Chrome extension version 2.1.2, \"customers/my_customer/apps/android/com.google.android.apps.docs\" for the Google Drive Android app's latest version.", "location": "path", "pattern": "^customers/[^/]+/apps/chrome/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleChromeManagementV1AppDetails"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.appdetails.readonly"]}}}, "web": {"methods": {"get": {"description": "Get a specific app for a customer by its resource name.", "flatPath": "v1/customers/{customersId}/apps/web/{webId}", "httpMethod": "GET", "id": "chromemanagement.customers.apps.web.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The app for which details are being queried. Examples: \"customers/my_customer/apps/chrome/gmbmikajjgmnabiglmofipeabaddhgne@2.1.2\" for the Save to Google Drive Chrome extension version 2.1.2, \"customers/my_customer/apps/android/com.google.android.apps.docs\" for the Google Drive Android app's latest version.", "location": "path", "pattern": "^customers/[^/]+/apps/web/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleChromeManagementV1AppDetails"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.appdetails.readonly"]}}}}}, "profiles": {"methods": {"delete": {"description": "Deletes the data collected from a Chrome browser profile.", "flatPath": "v1/customers/{customersId}/profiles/{profilesId}", "httpMethod": "DELETE", "id": "chromemanagement.customers.profiles.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Format: customers/{customer_id}/profiles/{profile_permanent_id}", "location": "path", "pattern": "^customers/[^/]+/profiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.profiles"]}, "get": {"description": "Gets a Chrome browser profile with customer ID and profile permanent ID.", "flatPath": "v1/customers/{customersId}/profiles/{profilesId}", "httpMethod": "GET", "id": "chromemanagement.customers.profiles.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Format: customers/{customer_id}/profiles/{profile_permanent_id}", "location": "path", "pattern": "^customers/[^/]+/profiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleChromeManagementVersionsV1ChromeBrowserProfile"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.profiles", "https://www.googleapis.com/auth/chrome.management.profiles.readonly"]}, "list": {"description": "Lists Chrome browser profiles of a customer based on the given search and sorting criteria.", "flatPath": "v1/customers/{customersId}/profiles", "httpMethod": "GET", "id": "chromemanagement.customers.profiles.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. The filter used to filter profiles. The following fields can be used in the filter: - profile_id - display_name - user_email - last_activity_time - last_policy_sync_time - last_status_report_time - first_enrollment_time - os_platform_type - os_version - browser_version - browser_channel - policy_count - extension_count - identity_provider - affiliation_state - os_platform_version - ouId Any of the above fields can be used to specify a filter, and filtering by multiple fields is supported with AND operator. String type fields and enum type fields support '=' and '!=' operators. The integer type and the timestamp type fields support '=', '!=', '<', '>', '<=' and '>=' operators. Timestamps expect an RFC-3339 formatted string (e.g. 2012-04-21T11:30:00-04:00). Wildcard '*' can be used with a string type field filter. In addition, string literal filtering is also supported, for example, 'ABC' as a filter maps to a filter that checks if any of the filterable string type fields contains 'ABC'. Organization unit number can be used as a filtering criteria here by specifying 'ouId = ${your_org_unit_id}', please note that only single OU ID matching is supported.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. The fields used to specify the ordering of the results. The supported fields are: - profile_id - display_name - user_email - last_activity_time - last_policy_sync_time - last_status_report_time - first_enrollment_time - os_platform_type - os_version - browser_version - browser_channel - policy_count - extension_count - identity_provider - affiliation_state - os_platform_version By default, sorting is in ascending order, to specify descending order for a field, a suffix \" desc\" should be added to the field name. The default ordering is the descending order of last_status_report_time.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of profiles to return. The default page size is 100 if page_size is unspecified, and the maximum page size allowed is 200.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The page token used to retrieve a specific page of the listing request.", "location": "query", "type": "string"}, "parent": {"description": "Required. Format: customers/{customer_id}", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/profiles", "response": {"$ref": "GoogleChromeManagementVersionsV1ListChromeBrowserProfilesResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.profiles", "https://www.googleapis.com/auth/chrome.management.profiles.readonly"]}}}, "reports": {"methods": {"countChromeBrowsersNeedingAttention": {"description": "Count of Chrome Browsers that have been recently enrolled, have new policy to be synced, or have no recent activity.", "flatPath": "v1/customers/{customersId}/reports:countChromeBrowsersNeedingAttention", "httpMethod": "GET", "id": "chromemanagement.customers.reports.countChromeBrowsersNeedingAttention", "parameterOrder": ["customer"], "parameters": {"customer": {"description": "Required. The customer ID or \"my_customer\" prefixed with \"customers/\".", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}, "orgUnitId": {"description": "Optional. The ID of the organizational unit. If omitted, all data will be returned.", "location": "query", "type": "string"}}, "path": "v1/{+customer}/reports:countChromeBrowsersNeedingAttention", "response": {"$ref": "GoogleChromeManagementV1CountChromeBrowsersNeedingAttentionResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.reports.readonly"]}, "countChromeCrashEvents": {"description": "Get a count of Chrome crash events.", "flatPath": "v1/customers/{customersId}/reports:countChromeCrashEvents", "httpMethod": "GET", "id": "chromemanagement.customers.reports.countChromeCrashEvents", "parameterOrder": ["customer"], "parameters": {"customer": {"description": "Customer ID.", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}, "filter": {"description": "Query string to filter results, AND-separated fields in EBNF syntax. Supported filter fields: * major_browser_version * minor_browser_version * browser_channel * device_platform * past_number_days Example: `major_browser_version = 'M115' AND past_number_days = '28'`.", "location": "query", "type": "string"}, "orderBy": {"description": "Field used to order results. Supported order by fields: * browser_version * count * date", "location": "query", "type": "string"}, "orgUnitId": {"description": "If specified, only count the number of crash events of the devices in this organizational unit.", "location": "query", "type": "string"}}, "path": "v1/{+customer}/reports:countChromeCrashEvents", "response": {"$ref": "GoogleChromeManagementV1CountChromeCrashEventsResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.reports.readonly"]}, "countChromeDevicesReachingAutoExpirationDate": {"description": "Generate report of the number of devices expiring in each month of the selected time frame. Devices are grouped by auto update expiration date and model. Further information can be found [here](https://support.google.com/chrome/a/answer/10564947).", "flatPath": "v1/customers/{customersId}/reports:countChromeDevicesReachingAutoExpirationDate", "httpMethod": "GET", "id": "chromemanagement.customers.reports.countChromeDevicesReachingAutoExpirationDate", "parameterOrder": ["customer"], "parameters": {"customer": {"description": "Required. The customer ID or \"my_customer\" prefixed with \"customers/\".", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}, "maxAueDate": {"description": "Optional. Maximum expiration date in format yyyy-mm-dd in UTC timezone. If included returns all devices that have already expired and devices with auto expiration date equal to or earlier than the maximum date.", "location": "query", "type": "string"}, "minAueDate": {"description": "Optional. Maximum expiration date in format yyyy-mm-dd in UTC timezone. If included returns all devices that have already expired and devices with auto expiration date equal to or later than the minimum date.", "location": "query", "type": "string"}, "orgUnitId": {"description": "Optional. The organizational unit ID, if omitted, will return data for all organizational units.", "location": "query", "type": "string"}}, "path": "v1/{+customer}/reports:countChromeDevicesReachingAutoExpirationDate", "response": {"$ref": "GoogleChromeManagementV1CountChromeDevicesReachingAutoExpirationDateResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.reports.readonly"]}, "countChromeDevicesThatNeedAttention": {"description": "Counts of ChromeOS devices that have not synced policies or have lacked user activity in the past 28 days, are out of date, or are not complaint. Further information can be found here https://support.google.com/chrome/a/answer/10564947", "flatPath": "v1/customers/{customersId}/reports:countChromeDevicesThatNeedAttention", "httpMethod": "GET", "id": "chromemanagement.customers.reports.countChromeDevicesThatNeedAttention", "parameterOrder": ["customer"], "parameters": {"customer": {"description": "Required. The customer ID or \"my_customer\" prefixed with \"customers/\".", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}, "orgUnitId": {"description": "Optional. The ID of the organizational unit. If omitted, all data will be returned.", "location": "query", "type": "string"}, "readMask": {"description": "Required. Mask of the fields that should be populated in the returned report.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+customer}/reports:countChromeDevicesThatNeedAttention", "response": {"$ref": "GoogleChromeManagementV1CountChromeDevicesThatNeedAttentionResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.reports.readonly"]}, "countChromeHardwareFleetDevices": {"description": "Counts of devices with a specific hardware specification from the requested hardware type (for example model name, processor type). Further information can be found here https://support.google.com/chrome/a/answer/10564947", "flatPath": "v1/customers/{customersId}/reports:countChromeHardwareFleetDevices", "httpMethod": "GET", "id": "chromemanagement.customers.reports.countChromeHardwareFleetDevices", "parameterOrder": ["customer"], "parameters": {"customer": {"description": "Required. The customer ID or \"my_customer\".", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}, "orgUnitId": {"description": "Optional. The ID of the organizational unit. If omitted, all data will be returned.", "location": "query", "type": "string"}, "readMask": {"description": "Required. Mask of the fields that should be populated in the returned report.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+customer}/reports:countChromeHardwareFleetDevices", "response": {"$ref": "GoogleChromeManagementV1CountChromeHardwareFleetDevicesResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.reports.readonly"]}, "countChromeVersions": {"description": "Generate report of installed Chrome versions.", "flatPath": "v1/customers/{customersId}/reports:countChromeVersions", "httpMethod": "GET", "id": "chromemanagement.customers.reports.countChromeVersions", "parameterOrder": ["customer"], "parameters": {"customer": {"description": "Required. Customer id or \"my_customer\" to use the customer associated to the account making the request.", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}, "filter": {"description": "Query string to filter results, AND-separated fields in EBNF syntax. Note: OR operations are not supported in this filter. Supported filter fields: * last_active_date", "location": "query", "type": "string"}, "orgUnitId": {"description": "The ID of the organizational unit.", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of results to return. Maximum and default are 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token to specify the page of the request to be returned.", "location": "query", "type": "string"}}, "path": "v1/{+customer}/reports:countChromeVersions", "response": {"$ref": "GoogleChromeManagementV1CountChromeVersionsResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.reports.readonly"]}, "countInstalledApps": {"description": "Generate report of app installations.", "flatPath": "v1/customers/{customersId}/reports:countInstalledApps", "httpMethod": "GET", "id": "chromemanagement.customers.reports.countInstalledApps", "parameterOrder": ["customer"], "parameters": {"customer": {"description": "Required. Customer id or \"my_customer\" to use the customer associated to the account making the request.", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}, "filter": {"description": "Query string to filter results, AND-separated fields in EBNF syntax. Note: OR operations are not supported in this filter. Supported filter fields: * app_name * app_type * install_type * number_of_permissions * total_install_count * latest_profile_active_date * permission_name * app_id * manifest_versions * risk_score", "location": "query", "type": "string"}, "orderBy": {"description": "Field used to order results. Supported order by fields: * app_name * app_type * install_type * number_of_permissions * total_install_count * app_id * manifest_versions * risk_score", "location": "query", "type": "string"}, "orgUnitId": {"description": "The ID of the organizational unit.", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of results to return. Maximum and default are 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token to specify the page of the request to be returned.", "location": "query", "type": "string"}}, "path": "v1/{+customer}/reports:countInstalledApps", "response": {"$ref": "GoogleChromeManagementV1CountInstalledAppsResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.reports.readonly"]}, "countPrintJobsByPrinter": {"description": "Get a summary of printing done by each printer.", "flatPath": "v1/customers/{customersId}/reports:countPrintJobsByPrinter", "httpMethod": "GET", "id": "chromemanagement.customers.reports.countPrintJobsByPrinter", "parameterOrder": ["customer"], "parameters": {"customer": {"description": "Required. Customer ID prefixed with \"customers/\" or \"customers/my_customer\" to use the customer associated to the account making the request.", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}, "filter": {"description": "Query string to filter results, AND-separated fields in EBNF syntax. Note: OR operations are not supported in this filter. Note: Only >= and <= comparators are supported in this filter. Supported filter fields: * complete_time", "location": "query", "type": "string"}, "orderBy": {"description": "Field used to order results. If omitted, results will be ordered in ascending order of the 'printer' field. Supported order_by fields: * printer * job_count * device_count * user_count", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of results to return. Maximum and default are 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token to specify the page of the response to be returned.", "location": "query", "type": "string"}, "printerOrgUnitId": {"description": "The ID of the organizational unit for printers. If specified, only data for printers from the specified organizational unit will be returned. If omitted, data for printers from all organizational units will be returned.", "location": "query", "type": "string"}}, "path": "v1/{+customer}/reports:countPrintJobsByPrinter", "response": {"$ref": "GoogleChromeManagementV1CountPrintJobsByPrinterResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.reports.readonly"]}, "countPrintJobsByUser": {"description": "Get a summary of printing done by each user.", "flatPath": "v1/customers/{customersId}/reports:countPrintJobsByUser", "httpMethod": "GET", "id": "chromemanagement.customers.reports.countPrintJobsByUser", "parameterOrder": ["customer"], "parameters": {"customer": {"description": "Required. Customer ID prefixed with \"customers/\" or \"customers/my_customer\" to use the customer associated to the account making the request.", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}, "filter": {"description": "Query string to filter results, AND-separated fields in EBNF syntax. Note: OR operations are not supported in this filter. Note: Only >= and <= comparators are supported in this filter. Supported filter fields: * complete_time", "location": "query", "type": "string"}, "orderBy": {"description": "Field used to order results. If omitted, results will be ordered in ascending order of the 'user_email' field. Supported order_by fields: * user_email * job_count * printer_count * device_count", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of results to return. Maximum and default are 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token to specify the page of the response to be returned.", "location": "query", "type": "string"}, "printerOrgUnitId": {"description": "The ID of the organizational unit for printers. If specified, only print jobs initiated with printers from the specified organizational unit will be counted. If omitted, all print jobs will be counted.", "location": "query", "type": "string"}}, "path": "v1/{+customer}/reports:countPrintJobsByUser", "response": {"$ref": "GoogleChromeManagementV1CountPrintJobsByUserResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.reports.readonly"]}, "enumeratePrintJobs": {"description": "Get a list of print jobs.", "flatPath": "v1/customers/{customersId}/reports:enumeratePrintJobs", "httpMethod": "GET", "id": "chromemanagement.customers.reports.enumeratePrintJobs", "parameterOrder": ["customer"], "parameters": {"customer": {"description": "Required. Customer ID prefixed with \"customers/\" or \"customers/my_customer\" to use the customer associated to the account making the request.", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}, "filter": {"description": "Query string to filter results, AND-separated fields in EBNF syntax. Note: OR operations are not supported in this filter. Note: Only >= and <= comparators are supported for `complete_time`. Note: Only = comparator supported for `user_id` and `printer_id`. Supported filter fields: * complete_time * printer_id * user_id", "location": "query", "type": "string"}, "orderBy": {"description": "Field used to order results. If not specified, results will be ordered in descending order of the `complete_time` field. Supported order by fields: * title * state * create_time * complete_time * document_page_count * color_mode * duplex_mode * printer * user_email", "location": "query", "type": "string"}, "pageSize": {"description": "The number of print jobs in the page from 0 to 100 inclusive, if page_size is not specified or zero, the size will be 50.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from a previous `EnumeratePrintJobs` call. Provide this to retrieve the subsequent page. If omitted, the first page of results will be returned. When paginating, all other parameters provided to `EnumeratePrintJobs` must match the call that provided the page token.", "location": "query", "type": "string"}, "printerOrgUnitId": {"description": "The ID of the organizational unit for printers. If specified, only print jobs submitted to printers from the specified organizational unit will be returned.", "location": "query", "type": "string"}}, "path": "v1/{+customer}/reports:enumeratePrintJobs", "response": {"$ref": "GoogleChromeManagementV1EnumeratePrintJobsResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.reports.readonly"]}, "findInstalledAppDevices": {"description": "Generate report of managed Chrome browser devices that have a specified app installed.", "flatPath": "v1/customers/{customersId}/reports:findInstalledAppDevices", "httpMethod": "GET", "id": "chromemanagement.customers.reports.findInstalledAppDevices", "parameterOrder": ["customer"], "parameters": {"appId": {"description": "Unique identifier of the app. For Chrome apps and extensions, the 32-character id (e.g. ehoadneljpdggcbbknedodolkkjodefl). For Android apps, the package name (e.g. com.evernote).", "location": "query", "type": "string"}, "appType": {"description": "Type of the app. Optional. If not provided, an app type will be inferred from the format of the app ID.", "enum": ["APP_TYPE_UNSPECIFIED", "EXTENSION", "APP", "THEME", "HOSTED_APP", "ANDROID_APP"], "enumDescriptions": ["App type not specified.", "Chrome extension.", "Chrome app.", "Chrome theme.", "Chrome hosted app.", "ARC++ app."], "location": "query", "type": "string"}, "customer": {"description": "Required. Customer id or \"my_customer\" to use the customer associated to the account making the request.", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}, "filter": {"description": "Query string to filter results, AND-separated fields in EBNF syntax. Note: OR operations are not supported in this filter. Supported filter fields: * last_active_date", "location": "query", "type": "string"}, "orderBy": {"description": "Field used to order results. Supported order by fields: * machine * device_id", "location": "query", "type": "string"}, "orgUnitId": {"description": "The ID of the organizational unit.", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of results to return. Maximum and default are 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token to specify the page of the request to be returned.", "location": "query", "type": "string"}}, "path": "v1/{+customer}/reports:findInstalledAppDevices", "response": {"$ref": "GoogleChromeManagementV1FindInstalledAppDevicesResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.reports.readonly"]}}}, "telemetry": {"resources": {"devices": {"methods": {"get": {"description": "Get telemetry device.", "flatPath": "v1/customers/{customersId}/telemetry/devices/{devicesId}", "httpMethod": "GET", "id": "chromemanagement.customers.telemetry.devices.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the `TelemetryDevice` to return.", "location": "path", "pattern": "^customers/[^/]+/telemetry/devices/[^/]+$", "required": true, "type": "string"}, "readMask": {"description": "Required. Read mask to specify which fields to return. Supported read_mask paths are: - name - org_unit_id - device_id - serial_number - cpu_info - cpu_status_report - memory_info - memory_status_report - network_info - network_diagnostics_report - network_status_report - os_update_status - graphics_info - graphics_status_report - battery_info - battery_status_report - storage_info - storage_status_report - thunderbolt_info - audio_status_report - boot_performance_report - heartbeat_status_report - network_bandwidth_report - peripherals_report - kiosk_app_status_report - app_report - runtime_counters_report ", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleChromeManagementV1TelemetryDevice"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.telemetry.readonly"]}, "list": {"description": "List all telemetry devices.", "flatPath": "v1/customers/{customersId}/telemetry/devices", "httpMethod": "GET", "id": "chromemanagement.customers.telemetry.devices.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Only include resources that match the filter. Requests that don't specify a \"reports_timestamp\" value will default to returning only recent reports. Specify \"reports_timestamp>=0\" to get all report data. Supported filter fields: - org_unit_id - serial_number - device_id - reports_timestamp The \"reports_timestamp\" filter accepts either the Unix Epoch milliseconds format or the RFC3339 UTC \"Zulu\" format with nanosecond resolution and up to nine fractional digits. Both formats should be surrounded by simple double quotes. Examples: \"2014-10-02T15:01:23Z\", \"2014-10-02T15:01:23.045123456Z\", \"*************\".", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of results to return. Default value is 100. Maximum value is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token to specify next page in the list.", "location": "query", "type": "string"}, "parent": {"description": "Required. Customer id or \"my_customer\" to use the customer associated to the account making the request.", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}, "readMask": {"description": "Required. Read mask to specify which fields to return. Supported read_mask paths are: - name - org_unit_id - device_id - serial_number - cpu_info - cpu_status_report - memory_info - memory_status_report - network_info - network_diagnostics_report - network_status_report - os_update_status - graphics_info - graphics_status_report - battery_info - battery_status_report - storage_info - storage_status_report - thunderbolt_info - audio_status_report - boot_performance_report - heartbeat_status_report - network_bandwidth_report - peripherals_report - kiosk_app_status_report - app_report - runtime_counters_report ", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+parent}/telemetry/devices", "response": {"$ref": "GoogleChromeManagementV1ListTelemetryDevicesResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.telemetry.readonly"]}}}, "events": {"methods": {"list": {"description": "List telemetry events.", "flatPath": "v1/customers/{customersId}/telemetry/events", "httpMethod": "GET", "id": "chromemanagement.customers.telemetry.events.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Only include resources that match the filter. Although this parameter is currently optional, this parameter will be required- please specify at least 1 event type. Supported filter fields: - device_id - user_id - device_org_unit_id - user_org_unit_id - timestamp - event_type The \"timestamp\" filter accepts either the Unix Epoch milliseconds format or the RFC3339 UTC \"Zulu\" format with nanosecond resolution and up to nine fractional digits. Both formats should be surrounded by simple double quotes. Examples: \"2014-10-02T15:01:23Z\", \"2014-10-02T15:01:23.045123456Z\", \"*************\".", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Maximum number of results to return. Default value is 100. Maximum value is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Token to specify next page in the list.", "location": "query", "type": "string"}, "parent": {"description": "Required. Customer id or \"my_customer\" to use the customer associated to the account making the request.", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}, "readMask": {"description": "Required. Read mask to specify which fields to return. Although currently required, this field will become optional, while the filter parameter with an event type will be come required. Supported read_mask paths are: - device - user - audio_severe_underrun_event - usb_peripherals_event - https_latency_change_event - network_state_change_event - wifi_signal_strength_event - vpn_connection_state_change_event - app_install_event - app_uninstall_event - app_launch_event - os_crash_event ", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+parent}/telemetry/events", "response": {"$ref": "GoogleChromeManagementV1ListTelemetryEventsResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.telemetry.readonly"]}}}, "notificationConfigs": {"methods": {"create": {"description": "Create a telemetry notification config.", "flatPath": "v1/customers/{customersId}/telemetry/notificationConfigs", "httpMethod": "POST", "id": "chromemanagement.customers.telemetry.notificationConfigs.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource where this notification config will be created. Format: `customers/{customer}`", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/telemetry/notificationConfigs", "request": {"$ref": "GoogleChromeManagementV1TelemetryNotificationConfig"}, "response": {"$ref": "GoogleChromeManagementV1TelemetryNotificationConfig"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.telemetry.readonly"]}, "delete": {"description": "Delete a telemetry notification config.", "flatPath": "v1/customers/{customersId}/telemetry/notificationConfigs/{notificationConfigsId}", "httpMethod": "DELETE", "id": "chromemanagement.customers.telemetry.notificationConfigs.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the notification config to delete. Format: `customers/{customer}/telemetry/notificationConfigs/{notification_config}`", "location": "path", "pattern": "^customers/[^/]+/telemetry/notificationConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.telemetry.readonly"]}, "list": {"description": "List all telemetry notification configs.", "flatPath": "v1/customers/{customersId}/telemetry/notificationConfigs", "httpMethod": "GET", "id": "chromemanagement.customers.telemetry.notificationConfigs.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of notification configs to return. The service may return fewer than this value. If unspecified, at most 100 notification configs will be returned. The maximum value is 100; values above 100 will be coerced to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListTelemetryNotificationConfigs` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListTelemetryNotificationConfigs` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent which owns the notification configs.", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/telemetry/notificationConfigs", "response": {"$ref": "GoogleChromeManagementV1ListTelemetryNotificationConfigsResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.telemetry.readonly"]}}}, "users": {"methods": {"get": {"description": "Get telemetry user.", "flatPath": "v1/customers/{customersId}/telemetry/users/{usersId}", "httpMethod": "GET", "id": "chromemanagement.customers.telemetry.users.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the `TelemetryUser` to return.", "location": "path", "pattern": "^customers/[^/]+/telemetry/users/[^/]+$", "required": true, "type": "string"}, "readMask": {"description": "Read mask to specify which fields to return. Supported read_mask paths are: - name - org_unit_id - user_id - user_email - user_device.device_id - user_device.audio_status_report - user_device.device_activity_report - user_device.network_bandwidth_report - user_device.peripherals_report - user_device.app_report ", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleChromeManagementV1TelemetryUser"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.telemetry.readonly"]}, "list": {"description": "List all telemetry users.", "flatPath": "v1/customers/{customersId}/telemetry/users", "httpMethod": "GET", "id": "chromemanagement.customers.telemetry.users.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Only include resources that match the filter. Supported filter fields: - user_id - user_org_unit_id ", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of results to return. Default value is 100. Maximum value is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token to specify next page in the list.", "location": "query", "type": "string"}, "parent": {"description": "Required. Customer id or \"my_customer\" to use the customer associated to the account making the request.", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}, "readMask": {"description": "Read mask to specify which fields to return. Supported read_mask paths are: - name - org_unit_id - user_id - user_email - user_device.device_id - user_device.audio_status_report - user_device.device_activity_report - user_device.network_bandwidth_report - user_device.peripherals_report - user_device.app_report ", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+parent}/telemetry/users", "response": {"$ref": "GoogleChromeManagementV1ListTelemetryUsersResponse"}, "scopes": ["https://www.googleapis.com/auth/chrome.management.telemetry.readonly"]}}}}}}}}, "revision": "********", "rootUrl": "https://chromemanagement.googleapis.com/", "schemas": {"GoogleChromeManagementV1AndroidAppInfo": {"description": "Android app information.", "id": "GoogleChromeManagementV1AndroidAppInfo", "properties": {"permissions": {"description": "Output only. Permissions requested by an Android app.", "items": {"$ref": "GoogleChromeManagementV1AndroidAppPermission"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleChromeManagementV1AndroidAppPermission": {"description": "Permission requested by an Android app.", "id": "GoogleChromeManagementV1AndroidAppPermission", "properties": {"type": {"description": "Output only. The type of the permission.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1AppDetails": {"description": "Resource representing app details.", "id": "GoogleChromeManagementV1AppDetails", "properties": {"androidAppInfo": {"$ref": "GoogleChromeManagementV1AndroidAppInfo", "description": "Output only. Android app information.", "readOnly": true}, "appId": {"description": "Output only. Unique store identifier for the item. Examples: \"gmbmikajjgmnabiglmofipeabaddhgne\" for the Save to Google Drive Chrome extension, \"com.google.android.apps.docs\" for the Google Drive Android app.", "readOnly": true, "type": "string"}, "chromeAppInfo": {"$ref": "GoogleChromeManagementV1ChromeAppInfo", "description": "Output only. Chrome Web Store app information.", "readOnly": true}, "description": {"description": "Output only. <PERSON><PERSON>'s description.", "readOnly": true, "type": "string"}, "detailUri": {"description": "Output only. The uri for the detail page of the item.", "readOnly": true, "type": "string"}, "displayName": {"description": "Output only. App's display name.", "readOnly": true, "type": "string"}, "firstPublishTime": {"description": "Output only. First published time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "homepageUri": {"description": "Output only. Home page or Website uri.", "readOnly": true, "type": "string"}, "iconUri": {"description": "Output only. A link to an image that can be used as an icon for the product.", "readOnly": true, "type": "string"}, "isPaidApp": {"description": "Output only. Indicates if the app has to be paid for OR has paid content.", "readOnly": true, "type": "boolean"}, "latestPublishTime": {"description": "Output only. Latest published time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Format: name=customers/{customer_id}/apps/{chrome|android|web}/{app_id}@{version}", "readOnly": true, "type": "string"}, "privacyPolicyUri": {"description": "Output only. The URI pointing to the privacy policy of the app, if it was provided by the developer. Version-specific field that will only be set when the requested app version is found.", "readOnly": true, "type": "string"}, "publisher": {"description": "Output only. The publisher of the item.", "readOnly": true, "type": "string"}, "reviewNumber": {"description": "Output only. Number of reviews received. Chrome Web Store review information will always be for the latest version of an app.", "format": "int64", "readOnly": true, "type": "string"}, "reviewRating": {"description": "Output only. The rating of the app (on 5 stars). Chrome Web Store review information will always be for the latest version of an app.", "format": "float", "readOnly": true, "type": "number"}, "revisionId": {"description": "Output only. App version. A new revision is committed whenever a new version of the app is published.", "readOnly": true, "type": "string"}, "serviceError": {"$ref": "GoogleRpcStatus", "description": "Output only. Information about a partial service error if applicable.", "readOnly": true}, "type": {"description": "Output only. App type.", "enum": ["APP_ITEM_TYPE_UNSPECIFIED", "CHROME", "ANDROID", "WEB"], "enumDescriptions": ["App type unspecified.", "Chrome app.", "ARC++ app.", "Web app."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1AppReport": {"description": "App report.", "id": "GoogleChromeManagementV1AppReport", "properties": {"reportTime": {"description": "Timestamp when the report was collected.", "format": "google-datetime", "type": "string"}, "usageData": {"description": "App usage data.", "items": {"$ref": "GoogleChromeManagementV1AppUsageData"}, "type": "array"}}, "type": "object"}, "GoogleChromeManagementV1AppUsageData": {"description": "App usage data.", "id": "GoogleChromeManagementV1AppUsageData", "properties": {"appId": {"description": "App id.", "type": "string"}, "appInstanceId": {"description": "Application instance id. This will be unique per window/instance.", "type": "string"}, "appType": {"description": "Type of app.", "enum": ["TELEMETRY_APPLICATION_TYPE_UNSPECIFIED", "APPLICATION_TYPE_ARC", "APPLICATION_TYPE_BUILT_IN", "APPLICATION_TYPE_CROSTINI", "APPLICATION_TYPE_CHROME_APP", "APPLICATION_TYPE_WEB", "APPLICATION_TYPE_MAC_OS", "APPLICATION_TYPE_PLUGIN_VM", "APPLICATION_TYPE_STANDALONE_BROWSER", "APPLICATION_TYPE_REMOTE", "APPLICATION_TYPE_BOREALIS", "APPLICATION_TYPE_SYSTEM_WEB", "APPLICATION_TYPE_STANDALONE_BROWSER_CHROME_APP", "APPLICATION_TYPE_EXTENSION", "APPLICATION_TYPE_STANDALONE_BROWSER_EXTENSION", "APPLICATION_TYPE_BRUSCHETTA"], "enumDeprecated": [false, false, true, false, false, false, false, false, true, false, false, false, true, false, true, false], "enumDescriptions": ["Application type unknown.", "Application type arc (Android app).", "Deprecated. This vaule is no longer used. Application type built-in.", "Application type Linux (via Crostini).", "Application type Chrome app.", "Application type web.", "Application type Mac OS.", "Application type Plugin VM.", "Deprecated. This vaule is no longer used. Application type standalone browser (Lacros browser app).", "Application type remote.", "Application type borealis.", "Application type system web.", "Deprecated. This vaule is no longer used. Application type standalone browser chrome app.", "Application type extension.", "Deprecated. This vaule is no longer used. Application type standalone browser extension.", "Application type bruschetta."], "type": "string"}, "runningDuration": {"description": "App foreground running time.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1AudioStatusReport": {"description": "Status data for storage. * This field is telemetry information and this will change over time as the device is utilized. * Data for this field is controlled via policy: [ReportDeviceAudioStatus](https://chromeenterprise.google/policies/#ReportDeviceAudioStatus) * Data Collection Frequency: 10 minutes * Default Data Reporting Frequency: 3 hours - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: No * Reported for affiliated users only: N/A * Granular permission needed: TELEMETRY_API_AUDIO_REPORT", "id": "GoogleChromeManagementV1AudioStatusReport", "properties": {"inputDevice": {"description": "Output only. Active input device's name.", "readOnly": true, "type": "string"}, "inputGain": {"description": "Output only. Active input device's gain in [0, 100].", "format": "int32", "readOnly": true, "type": "integer"}, "inputMute": {"description": "Output only. Is active input device mute or not.", "readOnly": true, "type": "boolean"}, "outputDevice": {"description": "Output only. Active output device's name.", "readOnly": true, "type": "string"}, "outputMute": {"description": "Output only. Is active output device mute or not.", "readOnly": true, "type": "boolean"}, "outputVolume": {"description": "Output only. Active output device's volume in [0, 100].", "format": "int32", "readOnly": true, "type": "integer"}, "reportTime": {"description": "Output only. Timestamp of when the sample was collected on device.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1BatteryInfo": {"description": "Information about the battery. * This field provides device information, which is static and will not change over time. * Data for this field is controlled via policy: [ReportDevicePowerStatus](https://chromeenterprise.google/policies/#ReportDevicePowerStatus) * Data Collection Frequency: Only at Upload * Default Data Reporting Frequency: 3 hours - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: No * Reported for affiliated users only: N/A * Granular permission needed: TELEMETRY_API_BATTERY_INFO", "id": "GoogleChromeManagementV1BatteryInfo", "properties": {"designCapacity": {"description": "Output only. Design capacity (mAmpere-hours).", "format": "int64", "readOnly": true, "type": "string"}, "designMinVoltage": {"description": "Output only. Designed minimum output voltage (mV)", "format": "int32", "readOnly": true, "type": "integer"}, "manufactureDate": {"$ref": "GoogleTypeDate", "description": "Output only. The date the battery was manufactured.", "readOnly": true}, "manufacturer": {"description": "Output only. Battery manufacturer.", "readOnly": true, "type": "string"}, "serialNumber": {"description": "Output only. Battery serial number.", "readOnly": true, "type": "string"}, "technology": {"description": "Output only. Technology of the battery. Example: Li-ion", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1BatterySampleReport": {"description": "Sampling data for battery. * This field is telemetry information and this will change over time as the device is utilized. * Data for this field is controlled via policy: [ReportDevicePowerStatus](https://chromeenterprise.google/policies/#ReportDevicePowerStatus) * Data Collection Frequency: Only at Upload * Default Data Reporting Frequency: 3 hours - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: No * Reported for affiliated users only: N/A", "id": "GoogleChromeManagementV1BatterySampleReport", "properties": {"chargeRate": {"description": "Output only. Battery charge percentage.", "format": "int32", "readOnly": true, "type": "integer"}, "current": {"description": "Output only. Battery current (mA).", "format": "int64", "readOnly": true, "type": "string"}, "dischargeRate": {"description": "Output only. The battery discharge rate measured in mW. Positive if the battery is being discharged, negative if it's being charged.", "format": "int32", "readOnly": true, "type": "integer"}, "remainingCapacity": {"description": "Output only. Battery remaining capacity (mAmpere-hours).", "format": "int64", "readOnly": true, "type": "string"}, "reportTime": {"description": "Output only. Timestamp of when the sample was collected on device", "format": "google-datetime", "readOnly": true, "type": "string"}, "status": {"description": "Output only. Battery status read from sysfs. Example: Discharging", "readOnly": true, "type": "string"}, "temperature": {"description": "Output only. Temperature in Celsius degrees.", "format": "int32", "readOnly": true, "type": "integer"}, "voltage": {"description": "Output only. Battery voltage (millivolt).", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1BatteryStatusReport": {"description": "Status data for battery. * This field is telemetry information and this will change over time as the device is utilized. * Data for this field is controlled via policy: [ReportDevicePowerStatus](https://chromeenterprise.google/policies/#ReportDevicePowerStatus) * Data Collection Frequency: Only at Upload * Default Data Reporting Frequency: 3 hours - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: No * Reported for affiliated users only: N/A * Granular permission needed: TELEMETRY_API_BATTERY_REPORT", "id": "GoogleChromeManagementV1BatteryStatusReport", "properties": {"batteryHealth": {"description": "Output only. Battery health.", "enum": ["BATTERY_HEALTH_UNSPECIFIED", "BATTERY_HEALTH_NORMAL", "BATTERY_REPLACE_SOON", "BATTERY_REPLACE_NOW"], "enumDescriptions": ["Health unknown.", "Battery is healthy, full charge capacity / design capacity > 80%", "Battery is moderately unhealthy and suggested to be replaced soon, full charge capacity / design capacity 75% - 80%", "Battery is unhealthy and suggested to be replaced, full charge capacity / design capacity < 75%"], "readOnly": true, "type": "string"}, "cycleCount": {"description": "Output only. Cycle count.", "format": "int32", "readOnly": true, "type": "integer"}, "fullChargeCapacity": {"description": "Output only. Full charge capacity (mAmpere-hours).", "format": "int64", "readOnly": true, "type": "string"}, "reportTime": {"description": "Output only. Timestamp of when the sample was collected on device", "format": "google-datetime", "readOnly": true, "type": "string"}, "sample": {"description": "Output only. Sampling data for the battery sorted in a decreasing order of report_time.", "items": {"$ref": "GoogleChromeManagementV1BatterySampleReport"}, "readOnly": true, "type": "array"}, "serialNumber": {"description": "Output only. Battery serial number.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1BootPerformanceReport": {"description": "Boot performance report of a device. * This field is telemetry information and this will change over time as the device is utilized. * Data for this field is controlled via policy: [ReportDeviceBootMode](https://chromeenterprise.google/policies/#ReportDeviceBootMode) * Data Collection Frequency: On every boot up event * Default Data Reporting Frequency: 3 hours - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: Yes * Reported for affiliated users only: N/A * Granular permission needed: TELEMETRY_API_OS_REPORT", "id": "GoogleChromeManagementV1BootPerformanceReport", "properties": {"bootUpDuration": {"description": "Total time to boot up.", "format": "google-duration", "type": "string"}, "bootUpTime": {"description": "The timestamp when power came on.", "format": "google-datetime", "type": "string"}, "reportTime": {"description": "Timestamp when the report was collected.", "format": "google-datetime", "type": "string"}, "shutdownDuration": {"description": "Total time since shutdown start to power off.", "format": "google-duration", "type": "string"}, "shutdownReason": {"description": "The shutdown reason.", "enum": ["SHUTDOWN_REASON_UNSPECIFIED", "USER_REQUEST", "SYSTEM_UPDATE", "LOW_BATTERY", "OTHER"], "enumDescriptions": ["Shutdown reason is not specified.", "User initiated.", "System update initiated.", "Shutdown due to low battery.", "Shutdown due to other reasons."], "type": "string"}, "shutdownTime": {"description": "The timestamp when shutdown.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1BrowserVersion": {"description": "Describes a browser version and its install count.", "id": "GoogleChromeManagementV1BrowserVersion", "properties": {"channel": {"description": "Output only. The release channel of the installed browser.", "enum": ["RELEASE_CHANNEL_UNSPECIFIED", "CANARY", "DEV", "BETA", "STABLE", "LTS"], "enumDescriptions": ["No release channel specified.", "Canary release channel.", "Dev release channel.", "Beta release channel.", "Stable release channel.", "Long-term support release channel."], "readOnly": true, "type": "string"}, "count": {"description": "Output only. Count grouped by device_system and major version", "format": "int64", "readOnly": true, "type": "string"}, "deviceOsVersion": {"description": "Output only. Version of the system-specified operating system.", "readOnly": true, "type": "string"}, "system": {"description": "Output only. The device operating system.", "enum": ["DEVICE_SYSTEM_UNSPECIFIED", "SYSTEM_OTHER", "SYSTEM_ANDROID", "SYSTEM_IOS", "SYSTEM_CROS", "SYSTEM_WINDOWS", "SYSTEM_MAC", "SYSTEM_LINUX"], "enumDescriptions": ["No operating system specified.", "Other operating system.", "Android operating system.", "Apple iOS operating system.", "ChromeOS operating system.", "Microsoft Windows operating system.", "Apple macOS operating system.", "Linux operating system."], "readOnly": true, "type": "string"}, "version": {"description": "Output only. The full version of the installed browser.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1ChromeAppInfo": {"description": "Chrome Web Store app information.", "id": "GoogleChromeManagementV1ChromeAppInfo", "properties": {"googleOwned": {"description": "Output only. Whether the app or extension is built and maintained by Google. Version-specific field that will only be set when the requested app version is found.", "readOnly": true, "type": "boolean"}, "isCwsHosted": {"description": "Output only. Whether the app or extension is in a published state in the Chrome Web Store.", "readOnly": true, "type": "boolean"}, "isExtensionPolicySupported": {"description": "Output only. Whether an app supports policy for extensions.", "readOnly": true, "type": "boolean"}, "isKioskOnly": {"description": "Output only. Whether the app is only for Kiosk mode on ChromeOS devices", "readOnly": true, "type": "boolean"}, "isTheme": {"description": "Output only. Whether the app or extension is a theme.", "readOnly": true, "type": "boolean"}, "kioskEnabled": {"description": "Output only. Whether this app is enabled for Kiosk mode on ChromeOS devices", "readOnly": true, "type": "boolean"}, "minUserCount": {"description": "Output only. The minimum number of users using this app.", "format": "int32", "readOnly": true, "type": "integer"}, "permissions": {"description": "Output only. Every custom permission requested by the app. Version-specific field that will only be set when the requested app version is found.", "items": {"$ref": "GoogleChromeManagementV1ChromeAppPermission"}, "readOnly": true, "type": "array"}, "siteAccess": {"description": "Output only. Every permission giving access to domains or broad host patterns. ( e.g. www.google.com). This includes the matches from content scripts as well as hosts in the permissions node of the manifest. Version-specific field that will only be set when the requested app version is found.", "items": {"$ref": "GoogleChromeManagementV1ChromeAppSiteAccess"}, "readOnly": true, "type": "array"}, "supportEnabled": {"description": "Output only. The app developer has enabled support for their app. Version-specific field that will only be set when the requested app version is found.", "readOnly": true, "type": "boolean"}, "type": {"description": "Output only. Types of an item in the Chrome Web Store", "enum": ["ITEM_TYPE_UNSPECIFIED", "EXTENSION", "OTHERS"], "enumDescriptions": ["Unspecified ItemType.", "Chrome Extensions.", "Any other type than extension."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1ChromeAppPermission": {"description": "Permission requested by a Chrome app or extension.", "id": "GoogleChromeManagementV1ChromeAppPermission", "properties": {"accessUserData": {"description": "Output only. If available, whether this permissions grants the app/extension access to user data.", "readOnly": true, "type": "boolean"}, "documentationUri": {"description": "Output only. If available, a URI to a page that has documentation for the current permission.", "readOnly": true, "type": "string"}, "type": {"description": "Output only. The type of the permission.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1ChromeAppRequest": {"description": "Details of an app installation request.", "id": "GoogleChromeManagementV1ChromeAppRequest", "properties": {"appDetails": {"description": "Output only. Format: app_details=customers/{customer_id}/apps/chrome/{app_id}", "readOnly": true, "type": "string"}, "appId": {"description": "Output only. Unique store identifier for the app. Example: \"gmbmikajjgmnabiglmofipeabaddhgne\" for the Save to Google Drive Chrome extension.", "readOnly": true, "type": "string"}, "detailUri": {"description": "Output only. The uri for the detail page of the item.", "readOnly": true, "type": "string"}, "displayName": {"description": "Output only. App's display name.", "readOnly": true, "type": "string"}, "iconUri": {"description": "Output only. A link to an image that can be used as an icon for the product.", "readOnly": true, "type": "string"}, "latestRequestTime": {"description": "Output only. The timestamp of the most recently made request for this app.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestCount": {"description": "Output only. Total count of requests for this app.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1ChromeAppSiteAccess": {"description": "Represent one host permission.", "id": "GoogleChromeManagementV1ChromeAppSiteAccess", "properties": {"hostMatch": {"description": "Output only. This can contain very specific hosts, or patterns like \"*.com\" for instance.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1CountChromeAppRequestsResponse": {"description": "Response containing summary of requested app installations.", "id": "GoogleChromeManagementV1CountChromeAppRequestsResponse", "properties": {"nextPageToken": {"description": "Token to specify the next page in the list.", "type": "string"}, "requestedApps": {"description": "Count of requested apps matching request.", "items": {"$ref": "GoogleChromeManagementV1ChromeAppRequest"}, "type": "array"}, "totalSize": {"description": "Total number of matching app requests.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleChromeManagementV1CountChromeBrowsersNeedingAttentionResponse": {"description": "Response containing counts for browsers that need attention.", "id": "GoogleChromeManagementV1CountChromeBrowsersNeedingAttentionResponse", "properties": {"noRecentActivityCount": {"description": "Number of browsers that haven’t had any recent activity", "format": "int64", "type": "string"}, "pendingBrowserUpdateCount": {"description": "Number of browsers that are pending an OS update", "format": "int64", "type": "string"}, "recentlyEnrolledCount": {"description": "Number of browsers that have been recently enrolled", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1CountChromeCrashEventsResponse": {"description": "Response contains a list of CrashEventCountByVersionPerDay which count the chrome crash at the certain date.", "id": "GoogleChromeManagementV1CountChromeCrashEventsResponse", "properties": {"crashEventCounts": {"description": "Crash event counts grouped by date and browser version.", "items": {"$ref": "GoogleChromeManagementV1CountChromeCrashEventsResponseCrashEventCount"}, "type": "array"}}, "type": "object"}, "GoogleChromeManagementV1CountChromeCrashEventsResponseCrashEventCount": {"description": "The `count` of the Chrome crash events at the `date`.", "id": "GoogleChromeManagementV1CountChromeCrashEventsResponseCrashEventCount", "properties": {"browserVersion": {"description": "Browser version this is counting.", "type": "string"}, "count": {"description": "Total count of crash events.", "format": "int64", "type": "string"}, "date": {"$ref": "GoogleTypeDate", "description": "Date of the crash event."}}, "type": "object"}, "GoogleChromeManagementV1CountChromeDevicesReachingAutoExpirationDateResponse": {"description": "Response containing a list of devices expiring in each month of a selected time frame. Counts are grouped by model and Auto Update Expiration date.", "id": "GoogleChromeManagementV1CountChromeDevicesReachingAutoExpirationDateResponse", "properties": {"deviceAueCountReports": {"description": "The list of reports sorted by auto update expiration date in ascending order.", "items": {"$ref": "GoogleChromeManagementV1DeviceAueCountReport"}, "type": "array"}}, "type": "object"}, "GoogleChromeManagementV1CountChromeDevicesThatNeedAttentionResponse": {"description": "Response containing counts for devices that need attention.", "id": "GoogleChromeManagementV1CountChromeDevicesThatNeedAttentionResponse", "properties": {"noRecentPolicySyncCount": {"description": "Number of ChromeOS devices have not synced policies in the past 28 days.", "format": "int64", "type": "string"}, "noRecentUserActivityCount": {"description": "Number of ChromeOS devices that have not seen any user activity in the past 28 days.", "format": "int64", "type": "string"}, "osVersionNotCompliantCount": {"description": "Number of devices whose OS version is not compliant.", "format": "int64", "type": "string"}, "pendingUpdate": {"description": "Number of devices that are pending an OS update.", "format": "int64", "type": "string"}, "unsupportedPolicyCount": {"description": "Number of devices that are unable to apply a policy due to an OS version mismatch.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1CountChromeHardwareFleetDevicesResponse": {"description": "Response containing a list of devices with a specific type of hardware specification from the requested hardware type.", "id": "GoogleChromeManagementV1CountChromeHardwareFleetDevicesResponse", "properties": {"cpuReports": {"description": "The DeviceHardwareCountReport for device cpu type (for example Intel(R) Core(TM) i7-10610U CPU @ 1.80GHz).", "items": {"$ref": "GoogleChromeManagementV1DeviceHardwareCountReport"}, "type": "array"}, "memoryReports": {"description": "The DeviceHardwareCountReport for device memory amount in gigabytes (for example 16).", "items": {"$ref": "GoogleChromeManagementV1DeviceHardwareCountReport"}, "type": "array"}, "modelReports": {"description": "The DeviceHardwareCountReport for device model type (for example Acer C7 Chromebook).", "items": {"$ref": "GoogleChromeManagementV1DeviceHardwareCountReport"}, "type": "array"}, "storageReports": {"description": "The DeviceHardwareCountReport for device storage amount in gigabytes (for example 128).", "items": {"$ref": "GoogleChromeManagementV1DeviceHardwareCountReport"}, "type": "array"}}, "type": "object"}, "GoogleChromeManagementV1CountChromeVersionsResponse": {"description": "Response containing requested browser versions details and counts.", "id": "GoogleChromeManagementV1CountChromeVersionsResponse", "properties": {"browserVersions": {"description": "List of all browser versions and their install counts.", "items": {"$ref": "GoogleChromeManagementV1BrowserVersion"}, "type": "array"}, "nextPageToken": {"description": "Token to specify the next page of the request.", "type": "string"}, "totalSize": {"description": "Total number browser versions matching request.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleChromeManagementV1CountInstalledAppsResponse": {"description": "Response containing details of queried installed apps.", "id": "GoogleChromeManagementV1CountInstalledAppsResponse", "properties": {"installedApps": {"description": "List of installed apps matching request.", "items": {"$ref": "GoogleChromeManagementV1InstalledApp"}, "type": "array"}, "nextPageToken": {"description": "Token to specify the next page of the request.", "type": "string"}, "totalSize": {"description": "Total number of installed apps matching request.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleChromeManagementV1CountPrintJobsByPrinterResponse": {"description": "Response containing a summary printing report for each printer from the specified organizational unit for the requested time interval.", "id": "GoogleChromeManagementV1CountPrintJobsByPrinterResponse", "properties": {"nextPageToken": {"description": "Pagination token for requesting the next page.", "type": "string"}, "printerReports": {"description": "List of PrinterReports matching request.", "items": {"$ref": "GoogleChromeManagementV1PrinterReport"}, "type": "array"}, "totalSize": {"description": "Total number of printers matching request.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1CountPrintJobsByUserResponse": {"description": "Response containing a summary printing report for each user that has initiated a print job with a printer from the specified organizational unit during the requested time interval.", "id": "GoogleChromeManagementV1CountPrintJobsByUserResponse", "properties": {"nextPageToken": {"description": "Pagination token for requesting the next page.", "type": "string"}, "totalSize": {"description": "Total number of users matching request.", "format": "int64", "type": "string"}, "userPrintReports": {"description": "List of UserPrintReports matching request.", "items": {"$ref": "GoogleChromeManagementV1UserPrintReport"}, "type": "array"}}, "type": "object"}, "GoogleChromeManagementV1CpuInfo": {"description": "CPU specifications for the device * This field provides device information, which is static and will not change over time. * Data for this field is controlled via policy: [ReportDeviceCpuInfo](https://chromeenterprise.google/policies/#ReportDeviceCpuInfo) * Data Collection Frequency: Only at Upload * Default Data Reporting Frequency: 3 hours - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: No * Reported for affiliated users only: N/A * Granular permission needed: TELEMETRY_API_CPU_INFO", "id": "GoogleChromeManagementV1CpuInfo", "properties": {"architecture": {"description": "Output only. Architecture type for the CPU. * This field provides device information, which is static and will not change over time. * Data for this field is controlled via policy: [ReportDeviceCpuInfo](https://chromeenterprise.google/policies/#ReportDeviceCpuInfo) * Data Collection Frequency: Only at Upload * Default Data Reporting Frequency: 3 hours - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: No * Reported for affiliated users only: N/A", "enum": ["ARCHITECTURE_UNSPECIFIED", "X64"], "enumDescriptions": ["Architecture unknown.", "x64 architecture"], "readOnly": true, "type": "string"}, "keylockerConfigured": {"description": "Output only. Whether keylocker is configured.`TRUE` = Enabled; `FALSE` = disabled. Only reported if keylockerSupported = `TRUE`.", "readOnly": true, "type": "boolean"}, "keylockerSupported": {"description": "Output only. Whether keylocker is supported.", "readOnly": true, "type": "boolean"}, "maxClockSpeed": {"description": "Output only. The max CPU clock speed in kHz.", "format": "int32", "readOnly": true, "type": "integer"}, "model": {"description": "Output only. The CPU model name. Example: Intel(R) Core(TM) i5-8250U CPU @ 1.60GHz", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1CpuStatusReport": {"description": "Provides information about the status of the CPU. * This field is telemetry information and this will change over time as the device is utilized. * Data for this field is controlled via policy: [ReportDeviceCpuInfo](https://chromeenterprise.google/policies/#ReportDeviceCpuInfo) * Data Collection Frequency: Every 10 minutes * Default Data Reporting Frequency: 3 hours - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: No * Reported for affiliated users only: N/A * Granular permission needed: TELEMETRY_API_CPU_REPORT", "id": "GoogleChromeManagementV1CpuStatusReport", "properties": {"cpuTemperatureInfo": {"description": "Output only. CPU temperature sample info per CPU core in Celsius", "items": {"$ref": "GoogleChromeManagementV1CpuTemperatureInfo"}, "readOnly": true, "type": "array"}, "cpuUtilizationPct": {"description": "Output only. Sample of CPU utilization (0-100 percent).", "format": "int32", "readOnly": true, "type": "integer"}, "reportTime": {"description": "Output only. The timestamp in milliseconds representing time at which this report was sampled.", "format": "google-datetime", "readOnly": true, "type": "string"}, "sampleFrequency": {"description": "Output only. Frequency the report is sampled.", "format": "google-duration", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1CpuTemperatureInfo": {"description": "CPU temperature of a device. Sampled per CPU core in Celsius. * This field is telemetry information and this will change over time as the device is utilized. * Data for this field is controlled via policy: [ReportDeviceCpuInfo](https://chromeenterprise.google/policies/#ReportDeviceCpuInfo) * Data Collection Frequency: Every 10 minutes * Default Data Reporting Frequency: 3 hours - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: No * Reported for affiliated users only: N/A", "id": "GoogleChromeManagementV1CpuTemperatureInfo", "properties": {"label": {"description": "Output only. CPU label. Example: Core 0", "readOnly": true, "type": "string"}, "temperatureCelsius": {"description": "Output only. CPU temperature in Celsius.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "GoogleChromeManagementV1Device": {"description": "Describes a device reporting Chrome browser information.", "id": "GoogleChromeManagementV1Device", "properties": {"deviceId": {"description": "Output only. The ID of the device that reported this Chrome browser information.", "readOnly": true, "type": "string"}, "machine": {"description": "Output only. The name of the machine within its local network.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1DeviceActivityReport": {"description": "Device activity report. * Granular permission needed: TELEMETRY_API_DEVICE_ACTIVITY_REPORT", "id": "GoogleChromeManagementV1DeviceActivityReport", "properties": {"deviceActivityState": {"description": "Output only. Device activity state.", "enum": ["DEVICE_ACTIVITY_STATE_UNSPECIFIED", "ACTIVE", "IDLE", "LOCKED"], "enumDescriptions": ["Device activity state is unspecified.", "Device is currently being used.", "Device is currently idle.", "<PERSON><PERSON> is currently locked."], "readOnly": true, "type": "string"}, "reportTime": {"description": "Output only. Timestamp of when the report was collected.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1DeviceAueCountReport": {"description": "Report for CountChromeDevicesPerAueDateResponse, contains the count of devices of a specific model and auto update expiration range.", "id": "GoogleChromeManagementV1DeviceAueCountReport", "properties": {"aueMonth": {"description": "Enum value of month corresponding to the auto update expiration date in UTC time zone. If the device is already expired, this field is empty.", "enum": ["MONTH_UNSPECIFIED", "JANUARY", "FEBRUARY", "MARCH", "APRIL", "MAY", "JUNE", "JULY", "AUGUST", "SEPTEMBER", "OCTOBER", "NOVEMBER", "DECEMBER"], "enumDescriptions": ["The unspecified month.", "The month of January.", "The month of February.", "The month of March.", "The month of April.", "The month of May.", "The month of June.", "The month of July.", "The month of August.", "The month of September.", "The month of October.", "The month of November.", "The month of December."], "type": "string"}, "aueYear": {"description": "Int value of year corresponding to the Auto Update Expiration date in UTC time zone. If the device is already expired, this field is empty.", "format": "int64", "type": "string"}, "count": {"description": "Count of devices of this model.", "format": "int64", "type": "string"}, "expired": {"description": "Boolean value for whether or not the device has already expired.", "type": "boolean"}, "model": {"description": "Public model name of the devices.", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1DeviceHardwareCountReport": {"description": "Report for CountChromeDevicesPerHardwareSpecResponse, contains the count of devices with a unique hardware specification.", "id": "GoogleChromeManagementV1DeviceHardwareCountReport", "properties": {"bucket": {"description": "Public name of the hardware specification.", "type": "string"}, "count": {"description": "Count of devices with a unique hardware specification.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1DeviceRequestingExtensionDetails": {"description": "Details of a device requesting an extension, including the name of the device and the justification of the request.", "id": "GoogleChromeManagementV1DeviceRequestingExtensionDetails", "properties": {"deviceName": {"description": "The name of a device that has requested the extension.", "type": "string"}, "justification": {"description": "Request justification as entered by the user.", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1DiskInfo": {"description": "Status of the single storage device.", "id": "GoogleChromeManagementV1DiskInfo", "properties": {"bytesReadThisSession": {"description": "Output only. Number of bytes read since last boot.", "format": "int64", "readOnly": true, "type": "string"}, "bytesWrittenThisSession": {"description": "Output only. Number of bytes written since last boot.", "format": "int64", "readOnly": true, "type": "string"}, "discardTimeThisSession": {"description": "Output only. Time spent discarding since last boot. Discarding is writing to clear blocks which are no longer in use. Supported on kernels 4.18+.", "format": "google-duration", "readOnly": true, "type": "string"}, "health": {"description": "Output only. Disk health.", "readOnly": true, "type": "string"}, "ioTimeThisSession": {"description": "Output only. Counts the time the disk and queue were busy, so unlike the fields above, parallel requests are not counted multiple times.", "format": "google-duration", "readOnly": true, "type": "string"}, "manufacturer": {"description": "Output only. Disk manufacturer.", "readOnly": true, "type": "string"}, "model": {"description": "Output only. Disk model.", "readOnly": true, "type": "string"}, "readTimeThisSession": {"description": "Output only. Time spent reading from disk since last boot.", "format": "google-duration", "readOnly": true, "type": "string"}, "serialNumber": {"description": "Output only. Disk serial number.", "readOnly": true, "type": "string"}, "sizeBytes": {"description": "Output only. Disk size.", "format": "int64", "readOnly": true, "type": "string"}, "type": {"description": "Output only. Disk type: eMMC / NVMe / ATA / SCSI.", "readOnly": true, "type": "string"}, "volumeIds": {"description": "Output only. Disk volumes.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "writeTimeThisSession": {"description": "Output only. Time spent writing to disk since last boot.", "format": "google-duration", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1DisplayDevice": {"description": "Information of a display device.", "id": "GoogleChromeManagementV1DisplayDevice", "properties": {"displayHeightMm": {"description": "Output only. Display height in millimeters.", "format": "int32", "readOnly": true, "type": "integer"}, "displayName": {"description": "Output only. Display device name.", "readOnly": true, "type": "string"}, "displayWidthMm": {"description": "Output only. Display width in millimeters.", "format": "int32", "readOnly": true, "type": "integer"}, "internal": {"description": "Output only. Is display internal or not.", "readOnly": true, "type": "boolean"}, "manufactureYear": {"description": "Output only. Year of manufacture.", "format": "int32", "readOnly": true, "type": "integer"}, "manufacturerId": {"description": "Output only. Three letter manufacturer ID.", "readOnly": true, "type": "string"}, "modelId": {"description": "Output only. Manufacturer product code.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "GoogleChromeManagementV1DisplayInfo": {"description": "Information for a display.", "id": "GoogleChromeManagementV1DisplayInfo", "properties": {"deviceId": {"description": "Output only. Represents the graphics card device id.", "format": "int64", "readOnly": true, "type": "string"}, "displayName": {"description": "Output only. Display device name.", "readOnly": true, "type": "string"}, "isInternal": {"description": "Output only. Indicates if display is internal or not.", "readOnly": true, "type": "boolean"}, "refreshRate": {"description": "Output only. Refresh rate in Hz.", "format": "int32", "readOnly": true, "type": "integer"}, "resolutionHeight": {"description": "Output only. Resolution height in pixels.", "format": "int32", "readOnly": true, "type": "integer"}, "resolutionWidth": {"description": "Output only. Resolution width in pixels.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "GoogleChromeManagementV1EnumeratePrintJobsResponse": {"description": "Response containing a list of print jobs.", "id": "GoogleChromeManagementV1EnumeratePrintJobsResponse", "properties": {"nextPageToken": {"description": "A token, which can be used in a subsequent request to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "printJobs": {"description": "List of requested print jobs.", "items": {"$ref": "GoogleChromeManagementV1PrintJob"}, "type": "array"}, "totalSize": {"description": "Total number of print jobs matching request.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1FetchDevicesRequestingExtensionResponse": {"description": "Response containing a list of devices that have requested the queried extension.", "id": "GoogleChromeManagementV1FetchDevicesRequestingExtensionResponse", "properties": {"deviceDetails": {"description": "Details of devices that have requested the queried extension.", "items": {"$ref": "GoogleChromeManagementV1DeviceRequestingExtensionDetails"}, "type": "array"}, "nextPageToken": {"description": "Optional. Token to specify the next page in the list. Token expires after 1 day.", "type": "string"}, "totalSize": {"description": "Optional. Total number of devices in response.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleChromeManagementV1FetchUsersRequestingExtensionResponse": {"description": "Response containing a list of users that have requested the queried extension.", "id": "GoogleChromeManagementV1FetchUsersRequestingExtensionResponse", "properties": {"nextPageToken": {"description": "Token to specify the next page in the list.", "type": "string"}, "totalSize": {"description": "Total number of users in response.", "format": "int32", "type": "integer"}, "userDetails": {"description": "Details of users that have requested the queried extension.", "items": {"$ref": "GoogleChromeManagementV1UserRequestingExtensionDetails"}, "type": "array"}}, "type": "object"}, "GoogleChromeManagementV1FindInstalledAppDevicesResponse": {"description": "Response containing a list of devices with queried app installed.", "id": "GoogleChromeManagementV1FindInstalledAppDevicesResponse", "properties": {"devices": {"description": "A list of devices which have the app installed. Sorted in ascending alphabetical order on the Device.machine field.", "items": {"$ref": "GoogleChromeManagementV1Device"}, "type": "array"}, "nextPageToken": {"description": "Token to specify the next page of the request.", "type": "string"}, "totalSize": {"description": "Total number of devices matching request.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleChromeManagementV1GraphicsAdapterInfo": {"description": "Information of a graphics adapter (GPU).", "id": "GoogleChromeManagementV1GraphicsAdapterInfo", "properties": {"adapter": {"description": "Output only. Adapter name. Example: Mesa DRI Intel(R) UHD Graphics 620 (Kabylake GT2).", "readOnly": true, "type": "string"}, "deviceId": {"description": "Output only. Represents the graphics card device id.", "format": "int64", "readOnly": true, "type": "string"}, "driverVersion": {"description": "Output only. Version of the GPU driver.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1GraphicsInfo": {"description": "Information of the graphics subsystem. * This field provides device information, which is static and will not change over time. * Data for this field is controlled via policy: [ReportDeviceGraphicsStatus](https://chromeenterprise.google/policies/#ReportDeviceGraphicsStatus) * Data Collection Frequency: Only at Upload * Default Data Reporting Frequency: 3 hours - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: No * Reported for affiliated users only: N/A * Granular permission needed: TELEMETRY_API_GRAPHICS_INFO", "id": "GoogleChromeManagementV1GraphicsInfo", "properties": {"adapterInfo": {"$ref": "GoogleChromeManagementV1GraphicsAdapterInfo", "description": "Output only. Information about the graphics adapter (GPU).", "readOnly": true}, "displayDevices": {"description": "Output only. Information about the display(s) of the device.", "items": {"$ref": "GoogleChromeManagementV1DisplayDevice"}, "readOnly": true, "type": "array"}, "eprivacySupported": {"description": "Output only. Is ePrivacy screen supported or not.", "readOnly": true, "type": "boolean"}, "touchScreenInfo": {"$ref": "GoogleChromeManagementV1TouchScreenInfo", "description": "Output only. Information about the internal touch screen(s) of the device.", "readOnly": true}}, "type": "object"}, "GoogleChromeManagementV1GraphicsStatusReport": {"description": "Information of the graphics subsystem. * This field is telemetry information and this will change over time as the device is utilized. * Data for this field is controlled via policy: [ReportDeviceGraphicsInfo](https://chromeenterprise.google/policies/#ReportDeviceGraphicsInfo) * Data Collection Frequency: 3 hours. * Default Data Reporting Frequency: 3 hours - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: No * Reported for affiliated users only: N/A * Granular permission needed: TELEMETRY_API_GRAPHICS_REPORT", "id": "GoogleChromeManagementV1GraphicsStatusReport", "properties": {"displays": {"description": "Output only. Information about the displays for the device.", "items": {"$ref": "GoogleChromeManagementV1DisplayInfo"}, "readOnly": true, "type": "array"}, "reportTime": {"description": "Output only. Time at which the graphics data was reported.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1HeartbeatStatusReport": {"description": "Heartbeat status report of a device. * Available for Kiosks * This field provides online/offline/unknown status of a device and will only be included if the status has changed (e.g. Online -> Offline) * Data for this field is controlled via policy: [HeartbeatEnabled](https://chromeenterprise.google/policies/#HeartbeatEnabled) [More Info](https://support.google.com/chrome/a/answer/6179663#:~:text=On%20the%20Chrome,device%20status%20alerts) * Heartbeat Frequency: 2 mins * Note: If a device goes offline, it can take up to 12 minutes for the online status of the device to be updated * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: N/A * Reported for affiliated users only: N/A * Granular permission needed: TELEMETRY_API_DEVICE_ACTIVITY_REPORT", "id": "GoogleChromeManagementV1HeartbeatStatusReport", "properties": {"reportTime": {"description": "Timestamp of when status changed was detected", "format": "google-datetime", "type": "string"}, "state": {"description": "State the device changed to", "enum": ["STATE_UNSPECIFIED", "UNKNOWN", "ONLINE", "OFFLINE"], "enumDescriptions": ["State not specified", "<PERSON><PERSON> is not eligible for heartbeat monitoring", "Device is online", "Device is offline"], "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1HttpsLatencyRoutineData": {"description": "Data that describes the result of the HTTPS latency diagnostics routine, with the HTTPS requests issued to Google websites.", "id": "GoogleChromeManagementV1HttpsLatencyRoutineData", "properties": {"latency": {"description": "Output only. HTTPS latency if routine succeeded or failed because of HIGH_LATENCY or VERY_HIGH_LATENCY.", "format": "google-duration", "readOnly": true, "type": "string"}, "problem": {"description": "Output only. HTTPS latency routine problem if a problem occurred.", "enum": ["HTTPS_LATENCY_PROBLEM_UNSPECIFIED", "FAILED_DNS_RESOLUTIONS", "FAILED_HTTPS_REQUESTS", "HIGH_LATENCY", "VERY_HIGH_LATENCY"], "enumDescriptions": ["HTTPS latency problem not specified.", "One or more DNS resolutions resulted in a failure.", "One or more HTTPS requests resulted in a failure.", "Average HTTPS request latency time between 500ms and 1000ms is high.", "Average HTTPS request latency time greater than 1000ms is very high."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1InstalledApp": {"description": "Describes an installed app.", "id": "GoogleChromeManagementV1InstalledApp", "properties": {"appId": {"description": "Output only. Unique identifier of the app. For Chrome apps and extensions, the 32-character id (e.g. ehoadneljpdggcbbknedodolkkjodefl). For Android apps, the package name (e.g. com.evernote).", "readOnly": true, "type": "string"}, "appInstallType": {"description": "Output only. How the app was installed.", "enum": ["APP_INSTALL_TYPE_UNSPECIFIED", "MULTIPLE", "NORMAL", "ADMIN", "DEVELOPMENT", "SIDELOAD", "OTHER"], "enumDescriptions": ["Application install type not specified.", "Multiple app install types.", "Normal app install type.", "Administrator app install type.", "Development app install type.", "Sideloaded app install type.", "Other app install type."], "readOnly": true, "type": "string"}, "appSource": {"description": "Output only. Source of the installed app.", "enum": ["APP_SOURCE_UNSPECIFIED", "CHROME_WEBSTORE", "PLAY_STORE"], "enumDescriptions": ["Application source not specified.", "Generally for extensions and Chrome apps.", "Play Store app."], "readOnly": true, "type": "string"}, "appType": {"description": "Output only. Type of the app.", "enum": ["APP_TYPE_UNSPECIFIED", "EXTENSION", "APP", "THEME", "HOSTED_APP", "ANDROID_APP"], "enumDescriptions": ["App type not specified.", "Chrome extension.", "Chrome app.", "Chrome theme.", "Chrome hosted app.", "ARC++ app."], "readOnly": true, "type": "string"}, "browserDeviceCount": {"description": "Output only. Count of browser devices with this app installed.", "format": "int64", "readOnly": true, "type": "string"}, "description": {"description": "Output only. Description of the installed app.", "readOnly": true, "type": "string"}, "disabled": {"description": "Output only. Whether the app is disabled.", "readOnly": true, "type": "boolean"}, "displayName": {"description": "Output only. Name of the installed app.", "readOnly": true, "type": "string"}, "homepageUri": {"description": "Output only. Homepage uri of the installed app.", "readOnly": true, "type": "string"}, "osUserCount": {"description": "Output only. Count of ChromeOS users with this app installed.", "format": "int64", "readOnly": true, "type": "string"}, "permissions": {"description": "Output only. Permissions of the installed app.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "riskAssessment": {"$ref": "GoogleChromeManagementV1RiskAssessmentData", "description": "Output only. If available, the risk assessment data about this extension.", "readOnly": true}}, "type": "object"}, "GoogleChromeManagementV1KioskAppStatusReport": {"description": "Kiosk app status report of a device. * Available for Kiosks * This field provides the app id and version number running on a kiosk device and the timestamp of when the report was last updated * Data for this field is controlled via policy: [ReportDeviceSessionStatus](https://chromeenterprise.google/policies/#ReportDeviceSessionStatus) * Data Collection Frequency: Only at Upload * Default Data Reporting Frequency: 3 hours - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: No * Reported for affiliated users only: N/A * Granular permission needed: TELEMETRY_API_APPS_REPORT", "id": "GoogleChromeManagementV1KioskAppStatusReport", "properties": {"appId": {"description": "App id of kiosk app for example \"mdmkkicfmmkgmpkmkdikhlbggogpicma\"", "type": "string"}, "appVersion": {"description": "App version number of kiosk app for example \"1.10.118\"", "type": "string"}, "reportTime": {"description": "Timestamp of when report was collected", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1ListTelemetryDevicesResponse": {"id": "GoogleChromeManagementV1ListTelemetryDevicesResponse", "properties": {"devices": {"description": "Telemetry devices returned in the response.", "items": {"$ref": "GoogleChromeManagementV1TelemetryDevice"}, "type": "array"}, "nextPageToken": {"description": "Token to specify next page in the list.", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1ListTelemetryEventsResponse": {"description": "Response message for listing telemetry events for a customer.", "id": "GoogleChromeManagementV1ListTelemetryEventsResponse", "properties": {"nextPageToken": {"description": "Token to specify next page in the list.", "type": "string"}, "telemetryEvents": {"description": "Telemetry events returned in the response.", "items": {"$ref": "GoogleChromeManagementV1TelemetryEvent"}, "type": "array"}}, "type": "object"}, "GoogleChromeManagementV1ListTelemetryNotificationConfigsResponse": {"description": "Response message for listing notification configs for a customer.", "id": "GoogleChromeManagementV1ListTelemetryNotificationConfigsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "telemetryNotificationConfigs": {"description": "The telemetry notification configs from the specified customer.", "items": {"$ref": "GoogleChromeManagementV1TelemetryNotificationConfig"}, "type": "array"}}, "type": "object"}, "GoogleChromeManagementV1ListTelemetryUsersResponse": {"description": "Response message for listing telemetry users for a customer.", "id": "GoogleChromeManagementV1ListTelemetryUsersResponse", "properties": {"nextPageToken": {"description": "Token to specify next page in the list.", "type": "string"}, "telemetryUsers": {"description": "Telemetry users returned in the response.", "items": {"$ref": "GoogleChromeManagementV1TelemetryUser"}, "type": "array"}}, "type": "object"}, "GoogleChromeManagementV1MemoryInfo": {"description": "Memory information of a device. * This field has both telemetry and device information: - `totalRamBytes` - Device information - `availableRamBytes` - Telemetry information - `totalMemoryEncryption` - Device information * Data for this field is controlled via policy: [ReportDeviceMemoryInfo](https://chromeenterprise.google/policies/#ReportDeviceMemoryInfo) * Data Collection Frequency: - `totalRamBytes` - Only at upload - `availableRamBytes` - Every 10 minutes - `totalMemoryEncryption` - at device startup * Default Data Reporting Frequency: - `totalRamBytes` - 3 hours - `availableRamBytes` - 3 hours - `totalMemoryEncryption` - at device startup - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: only for `totalMemoryEncryption` * Reported for affiliated users only: N/A * Granular permission needed: TELEMETRY_API_MEMORY_INFO", "id": "GoogleChromeManagementV1MemoryInfo", "properties": {"availableRamBytes": {"description": "Output only. Amount of available RAM in bytes.", "format": "int64", "readOnly": true, "type": "string"}, "totalMemoryEncryption": {"$ref": "GoogleChromeManagementV1TotalMemoryEncryptionInfo", "description": "Output only. Total memory encryption info for the device.", "readOnly": true}, "totalRamBytes": {"description": "Output only. Total RAM in bytes.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1MemoryStatusReport": {"description": "Contains samples of memory status reports. * This field is telemetry information and this will change over time as the device is utilized. * Data for this field is controlled via policy: [ReportDeviceMemoryInfo](https://chromeenterprise.google/policies/#ReportDeviceMemoryInfo) * Data Collection Frequency: Only at upload, SystemRamFreeByes is collected every 10 minutes * Default Data Reporting Frequency: Every 3 hours - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: No * Reported for affiliated users only: N/A * Granular permission needed: TELEMETRY_API_MEMORY_REPORT", "id": "GoogleChromeManagementV1MemoryStatusReport", "properties": {"pageFaults": {"description": "Output only. Number of page faults during this collection", "format": "int32", "readOnly": true, "type": "integer"}, "reportTime": {"description": "Output only. The timestamp in milliseconds representing time at which this report was sampled.", "format": "google-datetime", "readOnly": true, "type": "string"}, "sampleFrequency": {"description": "Output only. Frequency the report is sampled.", "format": "google-duration", "readOnly": true, "type": "string"}, "systemRamFreeBytes": {"description": "Output only. Amount of free RAM in bytes (unreliable due to Garbage Collection).", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1NetworkBandwidthReport": {"description": "Network bandwidth report. * Granular permission needed: TELEMETRY_API_NETWORK_REPORT", "id": "GoogleChromeManagementV1NetworkBandwidthReport", "properties": {"downloadSpeedKbps": {"description": "Output only. Download speed in kilobits per second.", "format": "int64", "readOnly": true, "type": "string"}, "reportTime": {"description": "Output only. Timestamp of when the report was collected.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1NetworkDevice": {"description": "Details about the network device. * This field provides device information, which is static and will not change over time. * Data for this field is controlled via policy: [ReportNetworkDeviceConfiguration](https://chromeenterprise.google/policies/#ReportNetworkDeviceConfiguration) * Data Collection Frequency: At device startup * Default Data Reporting Frequency: At device startup - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: Yes * Reported for affiliated users only: N/A", "id": "GoogleChromeManagementV1NetworkDevice", "properties": {"iccid": {"description": "Output only. The integrated circuit card ID associated with the device's sim card.", "readOnly": true, "type": "string"}, "imei": {"description": "Output only. IMEI (if applicable) of the corresponding network device.", "readOnly": true, "type": "string"}, "macAddress": {"description": "Output only. MAC address (if applicable) of the corresponding network device.", "readOnly": true, "type": "string"}, "mdn": {"description": "Output only. The mobile directory number associated with the device's sim card.", "readOnly": true, "type": "string"}, "meid": {"description": "Output only. MEID (if applicable) of the corresponding network device.", "readOnly": true, "type": "string"}, "type": {"description": "Output only. Network device type.", "enum": ["NETWORK_DEVICE_TYPE_UNSPECIFIED", "CELLULAR_DEVICE", "ETHERNET_DEVICE", "WIFI_DEVICE"], "enumDescriptions": ["Network device type not specified.", "Cellular device.", "Ethernet device.", "Wifi device."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1NetworkDiagnosticsReport": {"description": "Network testing results to determine the health of the device's network connection, for example whether the HTTPS latency is high or normal. * Granular permission needed: TELEMETRY_API_NETWORK_REPORT", "id": "GoogleChromeManagementV1NetworkDiagnosticsReport", "properties": {"httpsLatencyData": {"$ref": "GoogleChromeManagementV1HttpsLatencyRoutineData", "description": "Output only. HTTPS latency test data.", "readOnly": true}, "reportTime": {"description": "Output only. Timestamp of when the diagnostics were collected.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1NetworkInfo": {"description": "Network device information. * This field provides device information, which is static and will not change over time. * Data for this field is controlled via policy: [ReportNetworkDeviceConfiguration](https://chromeenterprise.google/policies/#ReportNetworkDeviceConfiguration) * Data Collection Frequency: At device startup * Default Data Reporting Frequency: At device startup - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: Yes * Reported for affiliated users only: N/A * Granular permission needed: TELEMETRY_API_NETWORK_INFO", "id": "GoogleChromeManagementV1NetworkInfo", "properties": {"networkDevices": {"description": "Output only. List of network devices.", "items": {"$ref": "GoogleChromeManagementV1NetworkDevice"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleChromeManagementV1NetworkStatusReport": {"description": "State of visible/configured networks. * This field is telemetry information and this will change over time as the device is utilized. * Data for this field is controlled via policy: [ReportNetworkStatus](https://chromeenterprise.google/policies/#ReportNetworkStatus) * Data Collection Frequency: 60 minutes * Default Data Reporting Frequency: 3 hours - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: Yes * Reported for affiliated users only: Yes * Granular permission needed: TELEMETRY_API_NETWORK_REPORT", "id": "GoogleChromeManagementV1NetworkStatusReport", "properties": {"connectionState": {"description": "Output only. Current connection state of the network.", "enum": ["NETWORK_CONNECTION_STATE_UNSPECIFIED", "ONLINE", "CONNECTED", "PORTAL", "CONNECTING", "NOT_CONNECTED"], "enumDescriptions": ["Network connection state unspecified.", "The network is connected and internet connectivity is available.", "The network is connected and not in a detected portal state, but internet connectivity may not be available.", "The network is connected but a portal state was detected. Internet connectivity may be limited.", "The network is in the process of connecting.", "The network is not connected."], "readOnly": true, "type": "string"}, "connectionType": {"description": "Output only. Network connection type.", "enum": ["NETWORK_TYPE_UNSPECIFIED", "CELLULAR", "ETHERNET", "TETHER", "VPN", "WIFI"], "enumDescriptions": ["Network connection type unspecified", "Cellular network connection.", "Ethernet network connection.", "Tether network connection.", "VPN network connection.", "Wifi network connection."], "readOnly": true, "type": "string"}, "encryptionOn": {"description": "Output only. Whether the wifi encryption key is turned off.", "readOnly": true, "type": "boolean"}, "gatewayIpAddress": {"description": "Output only. Gateway IP address.", "readOnly": true, "type": "string"}, "guid": {"description": "Output only. Network connection guid.", "readOnly": true, "type": "string"}, "lanIpAddress": {"description": "Output only. LAN IP address.", "readOnly": true, "type": "string"}, "receivingBitRateMbps": {"description": "Output only. Receiving bit rate measured in Megabits per second.", "format": "int64", "readOnly": true, "type": "string"}, "reportTime": {"description": "Output only. Time at which the network state was reported.", "format": "google-datetime", "readOnly": true, "type": "string"}, "sampleFrequency": {"description": "Output only. Frequency the report is sampled.", "format": "google-duration", "readOnly": true, "type": "string"}, "signalStrengthDbm": {"description": "Output only. Signal strength for wireless networks measured in decibels.", "format": "int32", "readOnly": true, "type": "integer"}, "transmissionBitRateMbps": {"description": "Output only. Transmission bit rate measured in Megabits per second.", "format": "int64", "readOnly": true, "type": "string"}, "transmissionPowerDbm": {"description": "Output only. Transmission power measured in decibels.", "format": "int32", "readOnly": true, "type": "integer"}, "wifiLinkQuality": {"description": "Output only. Wifi link quality. Value ranges from [0, 70]. 0 indicates no signal and 70 indicates a strong signal.", "format": "int64", "readOnly": true, "type": "string"}, "wifiPowerManagementEnabled": {"description": "Output only. Wifi power management enabled", "readOnly": true, "type": "boolean"}}, "type": "object"}, "GoogleChromeManagementV1OsUpdateStatus": {"description": "Contains information regarding the current OS update status. * This field is telemetry information and this will change over time as the device is utilized. * Data for this field is controlled via policy: [ReportDeviceOsUpdateStatus](https://chromeenterprise.google/policies/#ReportDeviceOsUpdateStatus) * Data Collection Frequency: Only at Upload * Default Data Reporting Frequency: 3 hours - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: No * Reported for affiliated users only: N/A * Granular permission needed: TELEMETRY_API_OS_REPORT", "id": "GoogleChromeManagementV1OsUpdateStatus", "properties": {"lastRebootTime": {"description": "Output only. Timestamp of the last reboot.", "format": "google-datetime", "readOnly": true, "type": "string"}, "lastUpdateCheckTime": {"description": "Output only. Timestamp of the last update check.", "format": "google-datetime", "readOnly": true, "type": "string"}, "lastUpdateTime": {"description": "Output only. Timestamp of the last successful update.", "format": "google-datetime", "readOnly": true, "type": "string"}, "newPlatformVersion": {"description": "Output only. New platform version of the os image being downloaded and applied. It is only set when update status is OS_IMAGE_DOWNLOAD_IN_PROGRESS or OS_UPDATE_NEED_REBOOT. Note this could be a dummy \"0.0.0.0\" for OS_UPDATE_NEED_REBOOT status for some edge cases, e.g. update engine is restarted without a reboot.", "readOnly": true, "type": "string"}, "newRequestedPlatformVersion": {"description": "Output only. New requested platform version from the pending updated kiosk app.", "readOnly": true, "type": "string"}, "updateState": {"description": "Output only. Current state of the os update.", "enum": ["UPDATE_STATE_UNSPECIFIED", "OS_IMAGE_DOWNLOAD_NOT_STARTED", "OS_IMAGE_DOWNLOAD_IN_PROGRESS", "OS_UPDATE_NEED_REBOOT"], "enumDescriptions": ["State unspecified.", "OS has not started downloading.", "OS has started download on device.", "<PERSON><PERSON> needs reboot to finish upload."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1PeripheralsReport": {"description": "Peripherals report. * Granular permission needed: TELEMETRY_API_PERIPHERALS_REPORT", "id": "GoogleChromeManagementV1PeripheralsReport", "properties": {"reportTime": {"description": "Output only. Timestamp of when the report was collected.", "format": "google-datetime", "readOnly": true, "type": "string"}, "usbPeripheralReport": {"description": "Reports of all usb connected devices.", "items": {"$ref": "GoogleChromeManagementV1UsbPeripheralReport"}, "type": "array"}}, "type": "object"}, "GoogleChromeManagementV1PrintJob": {"description": "Represents a request to print a document that has been submitted to a printer.", "id": "GoogleChromeManagementV1PrintJob", "properties": {"colorMode": {"description": "Color mode.", "enum": ["COLOR_MODE_UNSPECIFIED", "BLACK_AND_WHITE", "COLOR"], "enumDescriptions": ["Unspecified.", "Black and white.", "Color."], "type": "string"}, "completeTime": {"description": "Print job completion timestamp.", "format": "google-datetime", "type": "string"}, "copyCount": {"description": "Number of copies.", "format": "int32", "type": "integer"}, "createTime": {"description": "Print job creation timestamp.", "format": "google-datetime", "type": "string"}, "documentPageCount": {"description": "Number of pages in the document.", "format": "int32", "type": "integer"}, "duplexMode": {"description": "Duplex mode.", "enum": ["DUPLEX_MODE_UNSPECIFIED", "ONE_SIDED", "TWO_SIDED_LONG_EDGE", "TWO_SIDED_SHORT_EDGE"], "enumDescriptions": ["Unspecified.", "One-sided.", "Two-sided flipping over long edge.", "Two-sided flipping over short edge."], "type": "string"}, "id": {"description": "Unique ID of the print job.", "type": "string"}, "printer": {"description": "Name of the printer used for printing.", "type": "string"}, "printerId": {"description": "API ID of the printer used for printing.", "type": "string"}, "state": {"description": "The final state of the job.", "enum": ["STATE_UNSPECIFIED", "PRINTED", "CANCELLED", "FAILED"], "enumDescriptions": ["Print job is in an unspecified state.", "The document was successfully printed.", "Print job was cancelled.", "Print job failed."], "type": "string"}, "title": {"description": "The title of the document.", "type": "string"}, "userEmail": {"description": "The primary e-mail address of the user who submitted the print job.", "type": "string"}, "userId": {"description": "The unique Directory API ID of the user who submitted the print job.", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1PrinterReport": {"description": "Report for CountPrintJobsByPrinter, contains statistics on printer usage. Contains the total number of print jobs initiated with this printer, the number of users and the number of devices that have initiated at least one print job with this printer.", "id": "GoogleChromeManagementV1PrinterReport", "properties": {"deviceCount": {"description": "Number of chrome devices that have been used to send print jobs to the specified printer.", "format": "int64", "type": "string"}, "jobCount": {"description": "Number of print jobs sent to the printer.", "format": "int64", "type": "string"}, "printer": {"description": "Printer name.", "type": "string"}, "printerId": {"description": "Printer API ID.", "type": "string"}, "printerModel": {"description": "Printer model.", "type": "string"}, "userCount": {"description": "Number of users that have sent print jobs to the printer.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1RiskAssessment": {"description": "Risk assessment for a Chrome extension.", "id": "GoogleChromeManagementV1RiskAssessment", "properties": {"assessment": {"description": "Risk assessment for the extension. Currently, this is a numerical value, and its interpretation is specific to each risk assessment provider.", "type": "string"}, "detailsUrl": {"description": "A URL that a user can navigate to for more information about the risk assessment.", "type": "string"}, "version": {"description": "The version of the extension that this assessment applies to.", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1RiskAssessmentData": {"description": "Risk assessment data about an extension/app.", "id": "GoogleChromeManagementV1RiskAssessmentData", "properties": {"entries": {"description": "Individual risk assessments.", "items": {"$ref": "GoogleChromeManagementV1RiskAssessmentEntry"}, "type": "array"}, "overallRiskLevel": {"description": "Overall assessed risk level across all entries. This will be the highest risk level from all entries.", "enum": ["RISK_LEVEL_UNSPECIFIED", "RISK_LEVEL_LOW", "RISK_LEVEL_MEDIUM", "RISK_LEVEL_HIGH"], "enumDescriptions": ["Risk level not specified.", "Extension that represents a low risk.", "Extension that represents a medium risk.", "Extension that represents a high risk."], "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1RiskAssessmentEntry": {"description": "One risk assessment entry.", "id": "GoogleChromeManagementV1RiskAssessmentEntry", "properties": {"provider": {"description": "The risk assessment provider from which this entry comes from.", "enum": ["RISK_ASSESSMENT_PROVIDER_UNSPECIFIED", "RISK_ASSESSMENT_PROVIDER_CRXCAVATOR", "RISK_ASSESSMENT_PROVIDER_SPIN_AI"], "enumDescriptions": ["Default value when no provider is specified.", "CRXcavator.", "Spin.Ai."], "type": "string"}, "riskAssessment": {"$ref": "GoogleChromeManagementV1RiskAssessment", "description": "The details of the provider's risk assessment."}, "riskLevel": {"description": "The bucketed risk level for the risk assessment.", "enum": ["RISK_LEVEL_UNSPECIFIED", "RISK_LEVEL_LOW", "RISK_LEVEL_MEDIUM", "RISK_LEVEL_HIGH"], "enumDescriptions": ["Risk level not specified.", "Extension that represents a low risk.", "Extension that represents a medium risk.", "Extension that represents a high risk."], "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1RuntimeCountersReport": {"description": "Runtime counters retrieved from CPU. Currently the runtime counters telemetry is only supported by Intel vPro PSR on Gen 14+.", "id": "GoogleChromeManagementV1RuntimeCountersReport", "properties": {"enterHibernationCount": {"description": "Number of times that the device has entered into the hibernation state. Currently obtained via the PSR, count from S0->S4.", "format": "int64", "type": "string"}, "enterPoweroffCount": {"description": "Number of times that the device has entered into the power-off state. Currently obtained via the PSR, count from S0->S5.", "format": "int64", "type": "string"}, "enterSleepCount": {"description": "Number of times that the device has entered into the sleep state. Currently obtained via the PSR, count from S0->S3.", "format": "int64", "type": "string"}, "reportTime": {"description": "Timestamp when the report was collected.", "format": "google-datetime", "type": "string"}, "uptimeRuntimeDuration": {"description": "Total lifetime runtime. Currently always S0 runtime from Intel vPro PSR.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1StorageInfo": {"description": "Status data for storage. * This field is telemetry information and this will change over time as the device is utilized. * Data for this field is controlled via policy: [ReportDeviceStorageStatus](https://chromeenterprise.google/policies/#ReportDeviceStorageStatus) * Data Collection Frequency: Only at Upload * Default Data Reporting Frequency: 3 hours - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: No * Reported for affiliated users only: N/A * Granular permission needed: TELEMETRY_API_STORAGE_INFO", "id": "GoogleChromeManagementV1StorageInfo", "properties": {"availableDiskBytes": {"description": "The available space for user data storage in the device in bytes.", "format": "int64", "type": "string"}, "totalDiskBytes": {"description": "The total space for user data storage in the device in bytes.", "format": "int64", "type": "string"}, "volume": {"description": "Information for disk volumes", "items": {"$ref": "GoogleChromeManagementV1StorageInfoDiskVolume"}, "type": "array"}}, "type": "object"}, "GoogleChromeManagementV1StorageInfoDiskVolume": {"description": "Information for disk volumes", "id": "GoogleChromeManagementV1StorageInfoDiskVolume", "properties": {"storageFreeBytes": {"description": "Free storage space in bytes.", "format": "int64", "type": "string"}, "storageTotalBytes": {"description": "Total storage space in bytes.", "format": "int64", "type": "string"}, "volumeId": {"description": "Disk volume id.", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1StorageStatusReport": {"description": "Status data for storage. * This field is telemetry information and this will change over time as the device is utilized. * Data for this field is controlled via policy: [ReportDeviceStorageStatus](https://chromeenterprise.google/policies/#ReportDeviceStorageStatus) * Data Collection Frequency: Only at Upload * Default Data Reporting Frequency: 3 hours - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: No * Reported for affiliated users only: N/A * Granular permission needed: TELEMETRY_API_STORAGE_REPORT", "id": "GoogleChromeManagementV1StorageStatusReport", "properties": {"disk": {"description": "Output only. Reports on disk.", "items": {"$ref": "GoogleChromeManagementV1DiskInfo"}, "readOnly": true, "type": "array"}, "reportTime": {"description": "Output only. Timestamp of when the sample was collected on device", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1TelemetryAppInstallEvent": {"description": "App installation data.", "id": "GoogleChromeManagementV1TelemetryAppInstallEvent", "properties": {"appId": {"description": "App id. For PWAs this is the start URL, and for extensions this is the extension id.", "type": "string"}, "appInstallReason": {"description": "App installation reason.", "enum": ["APPLICATION_INSTALL_REASON_UNSPECIFIED", "APPLICATION_INSTALL_REASON_SYSTEM", "APPLICATION_INSTALL_REASON_POLICY", "APPLICATION_INSTALL_REASON_OEM", "APPLICATION_INSTALL_REASON_DEFAULT", "APPLICATION_INSTALL_REASON_SYNC", "APPLICATION_INSTALL_REASON_USER", "APPLICATION_INSTALL_REASON_SUB_APP", "APPLICATION_INSTALL_REASON_KIOSK", "APPLICATION_INSTALL_REASON_COMMAND_LINE"], "enumDescriptions": ["Application install reason is unknown.", "Application installed with the system and is considered part of the OS.", "Application installed by policy.", "Application installed by an original equipment manufacturer (OEM).", "Application installed by default, but is not considered a system app.", "Application installed by sync.", "Application installed by user action.", "Application installed bt SubApp API call.", "Application installed by Kiosk on Chrome OS.", "Application installed by command line argument."], "type": "string"}, "appInstallSource": {"description": "App installation source.", "enum": ["APPLICATION_INSTALL_SOURCE_UNSPECIFIED", "APPLICATION_INSTALL_SOURCE_SYSTEM", "APPLICATION_INSTALL_SOURCE_SYNC", "APPLICATION_INSTALL_SOURCE_PLAY_STORE", "APPLICATION_INSTALL_SOURCE_CHROME_WEB_STORE", "APPLICATION_INSTALL_SOURCE_BROWSER"], "enumDescriptions": ["Application install source is unknown.", "Application installed as part of Chrome OS.", "Application install source is a sync.", "Application install source is the Play store.", "Application install source is the Chrome web store.", "Application install source is a browser."], "type": "string"}, "appInstallTime": {"description": "App installation time depending on the app lifecycle.", "enum": ["APPLICATION_INSTALL_TIME_UNSPECIFIED", "APPLICATION_INSTALL_TIME_INIT", "APPLICATION_INSTALL_TIME_RUNNING"], "enumDescriptions": ["Application install time unknown.", "Application install is initialized.", "Application install is currently running."], "type": "string"}, "appType": {"description": "Type of app.", "enum": ["TELEMETRY_APPLICATION_TYPE_UNSPECIFIED", "APPLICATION_TYPE_ARC", "APPLICATION_TYPE_BUILT_IN", "APPLICATION_TYPE_CROSTINI", "APPLICATION_TYPE_CHROME_APP", "APPLICATION_TYPE_WEB", "APPLICATION_TYPE_MAC_OS", "APPLICATION_TYPE_PLUGIN_VM", "APPLICATION_TYPE_STANDALONE_BROWSER", "APPLICATION_TYPE_REMOTE", "APPLICATION_TYPE_BOREALIS", "APPLICATION_TYPE_SYSTEM_WEB", "APPLICATION_TYPE_STANDALONE_BROWSER_CHROME_APP", "APPLICATION_TYPE_EXTENSION", "APPLICATION_TYPE_STANDALONE_BROWSER_EXTENSION", "APPLICATION_TYPE_BRUSCHETTA"], "enumDeprecated": [false, false, true, false, false, false, false, false, true, false, false, false, true, false, true, false], "enumDescriptions": ["Application type unknown.", "Application type arc (Android app).", "Deprecated. This vaule is no longer used. Application type built-in.", "Application type Linux (via Crostini).", "Application type Chrome app.", "Application type web.", "Application type Mac OS.", "Application type Plugin VM.", "Deprecated. This vaule is no longer used. Application type standalone browser (Lacros browser app).", "Application type remote.", "Application type borealis.", "Application type system web.", "Deprecated. This vaule is no longer used. Application type standalone browser chrome app.", "Application type extension.", "Deprecated. This vaule is no longer used. Application type standalone browser extension.", "Application type bruschetta."], "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1TelemetryAppLaunchEvent": {"description": "App launch data.", "id": "GoogleChromeManagementV1TelemetryAppLaunchEvent", "properties": {"appId": {"description": "App id. For PWAs this is the start URL, and for extensions this is the extension id.", "type": "string"}, "appLaunchSource": {"description": "App launch source.", "enum": ["APPLICATION_LAUNCH_SOURCE_UNSPECIFIED", "APPLICATION_LAUNCH_SOURCE_APP_LIST_GRID", "APPLICATION_LAUNCH_SOURCE_APP_LIST_GRID_CONTEXT_MENU", "APPLICATION_LAUNCH_SOURCE_APP_LIST_QUERY", "APPLICATION_LAUNCH_SOURCE_APP_LIST_QUERY_CONTEXT_MENU", "APPLICATION_LAUNCH_SOURCE_APP_LIST_RECOMMENDATION", "APPLICATION_LAUNCH_SOURCE_PARENTAL_CONTROLS", "APPLICATION_LAUNCH_SOURCE_SHELF", "APPLICATION_LAUNCH_SOURCE_FILE_MANAGER", "APPLICATION_LAUNCH_SOURCE_LINK", "APPLICATION_LAUNCH_SOURCE_OMNIBOX", "APPLICATION_LAUNCH_SOURCE_CHROME_INTERNAL", "APPLICATION_LAUNCH_SOURCE_KEYBOARD", "APPLICATION_LAUNCH_SOURCE_OTHER_APP", "APPLICATION_LAUNCH_SOURCE_MENU", "APPLICATION_LAUNCH_SOURCE_INSTALLED_NOTIFICATION", "APPLICATION_LAUNCH_SOURCE_TEST", "APPLICATION_LAUNCH_SOURCE_ARC", "APPLICATION_LAUNCH_SOURCE_SHARESHEET", "APPLICATION_LAUNCH_SOURCE_RELEASE_NOTES_NOTIFICATION", "APPLICATION_LAUNCH_SOURCE_FULL_RESTORE", "APPLICATION_LAUNCH_SOURCE_SMART_TEXT_CONTEXT_MENU", "APPLICATION_LAUNCH_SOURCE_DISCOVER_TAB_NOTIFICATION", "APPLICATION_LAUNCH_SOURCE_MANAGEMENT_API", "APPLICATION_LAUNCH_SOURCE_KIOSK", "APPLICATION_LAUNCH_SOURCE_COMMAND_LINE", "APPLICATION_LAUNCH_SOURCE_BACKGROUND_MODE", "APPLICATION_LAUNCH_SOURCE_NEW_TAB_PAGE", "APPLICATION_LAUNCH_SOURCE_INTENT_URL", "APPLICATION_LAUNCH_SOURCE_OS_LOGIN", "APPLICATION_LAUNCH_SOURCE_PROTOCOL_HANDLER", "APPLICATION_LAUNCH_SOURCE_URL_HANDLER", "APPLICATION_LAUNCH_SOURCE_LOCK_SCREEN", "APPLICATION_LAUNCH_SOURCE_APP_HOME_PAGE", "APPLICATION_LAUNCH_SOURCE_REPARENTING", "APPLICATION_LAUNCH_SOURCE_PROFILE_MENU", "APPLICATION_LAUNCH_SOURCE_SYSTEM_TRAY_CALENDAR", "APPLICATION_LAUNCH_SOURCE_INSTALLER", "APPLICATION_LAUNCH_SOURCE_FIRST_RUN", "APPLICATION_LAUNCH_SOURCE_WELCOME_TOUR", "APPLICATION_LAUNCH_SOURCE_FOCUS_MODE", "APPLICATION_LAUNCH_SOURCE_SPARKY", "APPLICATION_LAUNCH_SOURCE_NAVIGATION_CAPTURING", "APPLICATION_LAUNCH_SOURCE_WEB_INSTALL_API"], "enumDescriptions": ["Application launch source unknown.", "Application launched from the grid of apps, not the search box.", "Application launched from the grid of apps, off of the context menu.", "Application launched from query-dependent results (larger icons).", "Application launched from query-dependent results, off of the context menu.", "Application launched from query-less recommendations (smaller icons).", "Application launched from the Parental Controls Settings section and Per App time notification.", "Application launched from shelf.", "Application launched from the file manager", "Application launched from left click on a link in the browser.", "Application launched from entering a URL in the Omnibox on the browser.", "Application launched from a Chrome internal call.", "Application launched from keyboard shortcut for opening app.", "Application launched from clicking a link in another app or WebUI.", "Application launched from menu.", "Application launched from the installed notification.", "Application launched from a test.", "Application launched from Arc.", "Application launched from Sharesheet.", "Application launched from the release notes notification.", "Application launched from a full restore.", "Application launched from a smart text selection context menu.", "Application launched from a discover tab notification.", "Application launched from the Management API.", "Application launched from kiosk.", "Application launched from the command line.", "Application launched from background mode.", "Application launched from the new tab page.", "Application launched from an intent URL.", "Application launched from OS login.", "Application launched from protocol handler.", "Application launched from URL handler.", "Application launched from lock screen app launcher.", "Application launched from app home (chrome://apps) page.", "Application launched from moving content into an app.", "Application launched from profile menu of installable chrome://password-manager WebUI.", "Application launched from system tray calendar.", "Application launched from source installer.", "Count first-run Help app launches separately so that we can understand the number of user-triggered launches.", "Application launched from welcome tour.", "Applicationed launched from focus panel.", "Application launched from experimental feature Sparky.", "Application launched from navigation capturing.", "Application launched from web install API."], "type": "string"}, "appType": {"description": "Type of app.", "enum": ["TELEMETRY_APPLICATION_TYPE_UNSPECIFIED", "APPLICATION_TYPE_ARC", "APPLICATION_TYPE_BUILT_IN", "APPLICATION_TYPE_CROSTINI", "APPLICATION_TYPE_CHROME_APP", "APPLICATION_TYPE_WEB", "APPLICATION_TYPE_MAC_OS", "APPLICATION_TYPE_PLUGIN_VM", "APPLICATION_TYPE_STANDALONE_BROWSER", "APPLICATION_TYPE_REMOTE", "APPLICATION_TYPE_BOREALIS", "APPLICATION_TYPE_SYSTEM_WEB", "APPLICATION_TYPE_STANDALONE_BROWSER_CHROME_APP", "APPLICATION_TYPE_EXTENSION", "APPLICATION_TYPE_STANDALONE_BROWSER_EXTENSION", "APPLICATION_TYPE_BRUSCHETTA"], "enumDeprecated": [false, false, true, false, false, false, false, false, true, false, false, false, true, false, true, false], "enumDescriptions": ["Application type unknown.", "Application type arc (Android app).", "Deprecated. This vaule is no longer used. Application type built-in.", "Application type Linux (via Crostini).", "Application type Chrome app.", "Application type web.", "Application type Mac OS.", "Application type Plugin VM.", "Deprecated. This vaule is no longer used. Application type standalone browser (Lacros browser app).", "Application type remote.", "Application type borealis.", "Application type system web.", "Deprecated. This vaule is no longer used. Application type standalone browser chrome app.", "Application type extension.", "Deprecated. This vaule is no longer used. Application type standalone browser extension.", "Application type bruschetta."], "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1TelemetryAppUninstallEvent": {"description": "App uninstall data.", "id": "GoogleChromeManagementV1TelemetryAppUninstallEvent", "properties": {"appId": {"description": "App id. For PWAs this is the start URL, and for extensions this is the extension id.", "type": "string"}, "appType": {"description": "Type of app.", "enum": ["TELEMETRY_APPLICATION_TYPE_UNSPECIFIED", "APPLICATION_TYPE_ARC", "APPLICATION_TYPE_BUILT_IN", "APPLICATION_TYPE_CROSTINI", "APPLICATION_TYPE_CHROME_APP", "APPLICATION_TYPE_WEB", "APPLICATION_TYPE_MAC_OS", "APPLICATION_TYPE_PLUGIN_VM", "APPLICATION_TYPE_STANDALONE_BROWSER", "APPLICATION_TYPE_REMOTE", "APPLICATION_TYPE_BOREALIS", "APPLICATION_TYPE_SYSTEM_WEB", "APPLICATION_TYPE_STANDALONE_BROWSER_CHROME_APP", "APPLICATION_TYPE_EXTENSION", "APPLICATION_TYPE_STANDALONE_BROWSER_EXTENSION", "APPLICATION_TYPE_BRUSCHETTA"], "enumDeprecated": [false, false, true, false, false, false, false, false, true, false, false, false, true, false, true, false], "enumDescriptions": ["Application type unknown.", "Application type arc (Android app).", "Deprecated. This vaule is no longer used. Application type built-in.", "Application type Linux (via Crostini).", "Application type Chrome app.", "Application type web.", "Application type Mac OS.", "Application type Plugin VM.", "Deprecated. This vaule is no longer used. Application type standalone browser (Lacros browser app).", "Application type remote.", "Application type borealis.", "Application type system web.", "Deprecated. This vaule is no longer used. Application type standalone browser chrome app.", "Application type extension.", "Deprecated. This vaule is no longer used. Application type standalone browser extension.", "Application type bruschetta."], "type": "string"}, "appUninstallSource": {"description": "App uninstall source.", "enum": ["APPLICATION_UNINSTALL_SOURCE_UNSPECIFIED", "APPLICATION_UNINSTALL_SOURCE_APP_LIST", "APPLICATION_UNINSTALL_SOURCE_APP_MANAGEMENT", "APPLICATION_UNINSTALL_SOURCE_SHELF", "APPLICATION_UNINSTALL_SOURCE_MIGRATION"], "enumDescriptions": ["Application uninstall source unknown.", "Application uninstalled from the App List (Launcher).", "Application uninstalled from the App Managedment page.", "Application uninstalled from the Shelf.", "Application uninstalled by app migration."], "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1TelemetryAudioSevereUnderrunEvent": {"description": "`TelemetryAudioSevereUnderrunEvent` is triggered when a audio devices run out of buffer data for more than 5 seconds. * Granular permission needed: TELEMETRY_API_AUDIO_REPORT", "id": "GoogleChromeManagementV1TelemetryAudioSevereUnderrunEvent", "properties": {}, "type": "object"}, "GoogleChromeManagementV1TelemetryDevice": {"description": "Telemetry data collected from a managed device. * Granular permission needed: TELEMETRY_API_DEVICE", "id": "GoogleChromeManagementV1TelemetryDevice", "properties": {"appReport": {"description": "Output only. App reports collected periodically sorted in a decreasing order of report_time.", "items": {"$ref": "GoogleChromeManagementV1AppReport"}, "readOnly": true, "type": "array"}, "audioStatusReport": {"description": "Output only. Audio reports collected periodically sorted in a decreasing order of report_time.", "items": {"$ref": "GoogleChromeManagementV1AudioStatusReport"}, "readOnly": true, "type": "array"}, "batteryInfo": {"description": "Output only. Information on battery specs for the device.", "items": {"$ref": "GoogleChromeManagementV1BatteryInfo"}, "readOnly": true, "type": "array"}, "batteryStatusReport": {"description": "Output only. Battery reports collected periodically.", "items": {"$ref": "GoogleChromeManagementV1BatteryStatusReport"}, "readOnly": true, "type": "array"}, "bootPerformanceReport": {"description": "Output only. Boot performance reports of the device.", "items": {"$ref": "GoogleChromeManagementV1BootPerformanceReport"}, "readOnly": true, "type": "array"}, "cpuInfo": {"description": "Output only. Information regarding CPU specs for the device.", "items": {"$ref": "GoogleChromeManagementV1CpuInfo"}, "readOnly": true, "type": "array"}, "cpuStatusReport": {"description": "Output only. CPU status reports collected periodically sorted in a decreasing order of report_time.", "items": {"$ref": "GoogleChromeManagementV1CpuStatusReport"}, "readOnly": true, "type": "array"}, "customer": {"description": "Output only. Google Workspace Customer whose enterprise enrolled the device.", "readOnly": true, "type": "string"}, "deviceId": {"description": "Output only. The unique Directory API ID of the device. This value is the same as the Admin Console's Directory API ID in the ChromeOS Devices tab", "readOnly": true, "type": "string"}, "graphicsInfo": {"$ref": "GoogleChromeManagementV1GraphicsInfo", "description": "Output only. Contains information regarding Graphic peripherals for the device.", "readOnly": true}, "graphicsStatusReport": {"description": "Output only. Graphics reports collected periodically.", "items": {"$ref": "GoogleChromeManagementV1GraphicsStatusReport"}, "readOnly": true, "type": "array"}, "heartbeatStatusReport": {"description": "Output only. Heartbeat status report containing timestamps periodically sorted in decreasing order of report_time", "items": {"$ref": "GoogleChromeManagementV1HeartbeatStatusReport"}, "readOnly": true, "type": "array"}, "kioskAppStatusReport": {"description": "Output only. Kiosk app status report for the kiosk device", "items": {"$ref": "GoogleChromeManagementV1KioskAppStatusReport"}, "readOnly": true, "type": "array"}, "memoryInfo": {"$ref": "GoogleChromeManagementV1MemoryInfo", "description": "Output only. Information regarding memory specs for the device.", "readOnly": true}, "memoryStatusReport": {"description": "Output only. Memory status reports collected periodically sorted decreasing by report_time.", "items": {"$ref": "GoogleChromeManagementV1MemoryStatusReport"}, "readOnly": true, "type": "array"}, "name": {"description": "Output only. Resource name of the device.", "readOnly": true, "type": "string"}, "networkBandwidthReport": {"description": "Output only. Network bandwidth reports collected periodically sorted in a decreasing order of report_time.", "items": {"$ref": "GoogleChromeManagementV1NetworkBandwidthReport"}, "readOnly": true, "type": "array"}, "networkDiagnosticsReport": {"description": "Output only. Network diagnostics collected periodically.", "items": {"$ref": "GoogleChromeManagementV1NetworkDiagnosticsReport"}, "readOnly": true, "type": "array"}, "networkInfo": {"$ref": "GoogleChromeManagementV1NetworkInfo", "description": "Output only. Network devices information.", "readOnly": true}, "networkStatusReport": {"description": "Output only. Network specs collected periodically.", "items": {"$ref": "GoogleChromeManagementV1NetworkStatusReport"}, "readOnly": true, "type": "array"}, "orgUnitId": {"description": "Output only. Organization unit ID of the device.", "readOnly": true, "type": "string"}, "osUpdateStatus": {"description": "Output only. Contains relevant information regarding ChromeOS update status.", "items": {"$ref": "GoogleChromeManagementV1OsUpdateStatus"}, "readOnly": true, "type": "array"}, "peripheralsReport": {"description": "Output only. Peripherals reports collected periodically sorted in a decreasing order of report_time.", "items": {"$ref": "GoogleChromeManagementV1PeripheralsReport"}, "readOnly": true, "type": "array"}, "runtimeCountersReport": {"description": "Output only. Runtime counters reports collected device lifetime runtime, as well as the counts of S0->S3, S0->S4, and S0->S5 transitions, meaning entering into sleep, hibernation, and power-off states", "items": {"$ref": "GoogleChromeManagementV1RuntimeCountersReport"}, "readOnly": true, "type": "array"}, "serialNumber": {"description": "Output only. Device serial number. This value is the same as the Admin Console's Serial Number in the ChromeOS Devices tab.", "readOnly": true, "type": "string"}, "storageInfo": {"$ref": "GoogleChromeManagementV1StorageInfo", "description": "Output only. Information of storage specs for the device.", "readOnly": true}, "storageStatusReport": {"description": "Output only. Storage reports collected periodically.", "items": {"$ref": "GoogleChromeManagementV1StorageStatusReport"}, "readOnly": true, "type": "array"}, "thunderboltInfo": {"description": "Output only. Information on Thunderbolt bus.", "items": {"$ref": "GoogleChromeManagementV1ThunderboltInfo"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleChromeManagementV1TelemetryDeviceInfo": {"description": "Information about a device associated with telemetry data. * Granular Permission needed: TELEMETRY_API_DEVICE", "id": "GoogleChromeManagementV1TelemetryDeviceInfo", "properties": {"deviceId": {"description": "Output only. The unique Directory API ID of the device. This value is the same as the Admin Console's Directory API ID in the ChromeOS Devices tab.", "readOnly": true, "type": "string"}, "orgUnitId": {"description": "Output only. Organization unit ID of the device.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1TelemetryEvent": {"description": "Telemetry data reported by a managed device.", "id": "GoogleChromeManagementV1TelemetryEvent", "properties": {"appInstallEvent": {"$ref": "GoogleChromeManagementV1TelemetryAppInstallEvent", "description": "Output only. Payload for app install event. Present only when `event_type` is `APP_INSTALLED`.", "readOnly": true}, "appLaunchEvent": {"$ref": "GoogleChromeManagementV1TelemetryAppLaunchEvent", "description": "Output only. Payload for app launch event.Present only when `event_type` is `APP_LAUNCHED`.", "readOnly": true}, "appUninstallEvent": {"$ref": "GoogleChromeManagementV1TelemetryAppUninstallEvent", "description": "Output only. Payload for app uninstall event. Present only when `event_type` is `APP_UNINSTALLED`.", "readOnly": true}, "audioSevereUnderrunEvent": {"$ref": "GoogleChromeManagementV1TelemetryAudioSevereUnderrunEvent", "description": "Output only. Payload for audio severe underrun event. Present only when the `event_type` field is `AUDIO_SEVERE_UNDERRUN`.", "readOnly": true}, "device": {"$ref": "GoogleChromeManagementV1TelemetryDeviceInfo", "description": "Output only. Information about the device associated with the event.", "readOnly": true}, "eventType": {"description": "The event type of the current event.", "enum": ["EVENT_TYPE_UNSPECIFIED", "AUDIO_SEVERE_UNDERRUN", "NETWORK_STATE_CHANGE", "USB_ADDED", "USB_REMOVED", "NETWORK_HTTPS_LATENCY_CHANGE", "WIFI_SIGNAL_STRENGTH_LOW", "WIFI_SIGNAL_STRENGTH_RECOVERED", "VPN_CONNECTION_STATE_CHANGE", "APP_INSTALLED", "APP_UNINSTALLED", "APP_LAUNCHED", "OS_CRASH"], "enumDescriptions": ["Event type unknown.", "Triggered when a audio devices run out of buffer data for more than 5 seconds.", "Triggered immediately on any changes to a network connection.", "Triggered when USB devices are added.", "Triggered when USB devices are removed.", "Triggered when a new HTTPS latency problem was detected or the device has recovered form an existing HTTPS latency problem.", "Triggered when connected WiFi network signal strength drops below -70dBm.", "Triggered when connected WiFi network signal strength is recovered from a signal drop.", "Triggered on changes to VPN connections.", "Triggered when an app is installed.", "Triggered when an app is uninstalled.", "Triggered when an app is launched.", "Triggered when a crash occurs."], "type": "string"}, "httpsLatencyChangeEvent": {"$ref": "GoogleChromeManagementV1TelemetryHttpsLatencyChangeEvent", "description": "Output only. Payload for HTTPS latency change event. Present only when `event_type` is `NETWORK_HTTPS_LATENCY_CHANGE`.", "readOnly": true}, "name": {"description": "Output only. Resource name of the event.", "readOnly": true, "type": "string"}, "networkStateChangeEvent": {"$ref": "GoogleChromeManagementV1TelemetryNetworkConnectionStateChangeEvent", "description": "Output only. Payload for network connection state change event. Present only when `event_type` is `NETWORK_STATE_CHANGE`.", "readOnly": true}, "osCrashEvent": {"$ref": "GoogleChromeManagementV1TelemetryOsCrashEvent", "description": "Output only. Payload for OS crash event. Present only when `event_type` is `OS_CRASH`.", "readOnly": true}, "reportTime": {"description": "Timestamp that represents when the event was reported.", "format": "google-datetime", "type": "string"}, "usbPeripheralsEvent": {"$ref": "GoogleChromeManagementV1TelemetryUsbPeripheralsEvent", "description": "Output only. Payload for usb peripherals event. Present only when the `event_type` field is either `USB_ADDED` or `USB_REMOVED`.", "readOnly": true}, "user": {"$ref": "GoogleChromeManagementV1TelemetryUserInfo", "description": "Output only. Information about the user associated with the event.", "readOnly": true}, "vpnConnectionStateChangeEvent": {"$ref": "GoogleChromeManagementV1TelemetryNetworkConnectionStateChangeEvent", "description": "Output only. Payload for VPN connection state change event. Present only when `event_type` is `VPN_CONNECTION_STATE_CHANGE`.", "readOnly": true}, "wifiSignalStrengthEvent": {"$ref": "GoogleChromeManagementV1TelemetryNetworkSignalStrengthEvent", "description": "Output only. Payload for WiFi signal strength events. Present only when `event_type` is `WIFI_SIGNAL_STRENGTH_LOW` or `WIFI_SIGNAL_STRENGTH_RECOVERED`.", "readOnly": true}}, "type": "object"}, "GoogleChromeManagementV1TelemetryEventNotificationFilter": {"description": "Configures how the telemetry events should be filtered.", "id": "GoogleChromeManagementV1TelemetryEventNotificationFilter", "properties": {"eventTypes": {"description": "Only sends the notifications for events of these types. Must not be empty.", "items": {"enum": ["EVENT_TYPE_UNSPECIFIED", "AUDIO_SEVERE_UNDERRUN", "NETWORK_STATE_CHANGE", "USB_ADDED", "USB_REMOVED", "NETWORK_HTTPS_LATENCY_CHANGE", "WIFI_SIGNAL_STRENGTH_LOW", "WIFI_SIGNAL_STRENGTH_RECOVERED", "VPN_CONNECTION_STATE_CHANGE", "APP_INSTALLED", "APP_UNINSTALLED", "APP_LAUNCHED", "OS_CRASH"], "enumDescriptions": ["Event type unknown.", "Triggered when a audio devices run out of buffer data for more than 5 seconds.", "Triggered immediately on any changes to a network connection.", "Triggered when USB devices are added.", "Triggered when USB devices are removed.", "Triggered when a new HTTPS latency problem was detected or the device has recovered form an existing HTTPS latency problem.", "Triggered when connected WiFi network signal strength drops below -70dBm.", "Triggered when connected WiFi network signal strength is recovered from a signal drop.", "Triggered on changes to VPN connections.", "Triggered when an app is installed.", "Triggered when an app is uninstalled.", "Triggered when an app is launched.", "Triggered when a crash occurs."], "type": "string"}, "type": "array"}}, "type": "object"}, "GoogleChromeManagementV1TelemetryHttpsLatencyChangeEvent": {"description": "Https latency routine is run periodically and `TelemetryHttpsLatencyChangeEvent` is triggered if a latency problem was detected or if the device has recovered from a latency problem. * Granular permission needed: TELEMETRY_API_NETWORK_REPORT", "id": "GoogleChromeManagementV1TelemetryHttpsLatencyChangeEvent", "properties": {"httpsLatencyRoutineData": {"$ref": "GoogleChromeManagementV1HttpsLatencyRoutineData", "description": "HTTPS latency routine data that triggered the event."}, "httpsLatencyState": {"description": "Current HTTPS latency state.", "enum": ["HTTPS_LATENCY_STATE_UNSPECIFIED", "RECOVERY", "PROBLEM"], "enumDescriptions": ["HTTPS latency state is unspecified.", "HTTPS latency recovered from a problem.", "HTTPS latency problem."], "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1TelemetryNetworkConnectionStateChangeEvent": {"description": "`TelemetryNetworkConnectionStateChangeEvent` is triggered on network connection state changes. * Granular permission needed: TELEMETRY_API_NETWORK_REPORT", "id": "GoogleChromeManagementV1TelemetryNetworkConnectionStateChangeEvent", "properties": {"connectionState": {"description": "Current connection state of the network.", "enum": ["NETWORK_CONNECTION_STATE_UNSPECIFIED", "ONLINE", "CONNECTED", "PORTAL", "CONNECTING", "NOT_CONNECTED"], "enumDescriptions": ["Network connection state unspecified.", "The network is connected and internet connectivity is available.", "The network is connected and not in a detected portal state, but internet connectivity may not be available.", "The network is connected but a portal state was detected. Internet connectivity may be limited.", "The network is in the process of connecting.", "The network is not connected."], "type": "string"}, "guid": {"description": "Unique identifier of the network.", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1TelemetryNetworkSignalStrengthEvent": {"description": "`TelemetryNetworkSignalStrengthEvent` is triggered on WiFi signal strength events. * Granular permission needed: TELEMETRY_API_NETWORK_REPORT", "id": "GoogleChromeManagementV1TelemetryNetworkSignalStrengthEvent", "properties": {"guid": {"description": "Unique identifier of the network.", "type": "string"}, "signalStrengthDbm": {"description": "Signal strength RSSI value.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleChromeManagementV1TelemetryNotificationConfig": {"description": "Configuration to receive notifications of telemetry data.", "id": "GoogleChromeManagementV1TelemetryNotificationConfig", "properties": {"customer": {"description": "Output only. Google Workspace customer that owns the resource.", "readOnly": true, "type": "string"}, "filter": {"$ref": "GoogleChromeManagementV1TelemetryNotificationFilter", "description": "Only send notifications for telemetry data matching this filter."}, "googleCloudPubsubTopic": {"description": "The pubsub topic to which notifications are published to.", "type": "string"}, "name": {"description": "Output only. Resource name of the notification configuration.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1TelemetryNotificationFilter": {"description": "Configures how the telemetry data should be filtered.", "id": "GoogleChromeManagementV1TelemetryNotificationFilter", "properties": {"deviceId": {"description": "If set, only sends notifications for telemetry data coming from this device.", "type": "string"}, "deviceOrgUnitId": {"description": "If set, only sends notifications for telemetry data coming from devices in this org unit.", "type": "string"}, "telemetryEventNotificationFilter": {"$ref": "GoogleChromeManagementV1TelemetryEventNotificationFilter", "description": "Only sends notifications for the telemetry events matching this filter."}, "userEmail": {"description": "If set, only sends notifications for telemetry data coming from devices owned by this user.", "type": "string"}, "userOrgUnitId": {"description": "If set, only sends notifications for telemetry data coming from devices owned by users in this org unit.", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1TelemetryOsCrashEvent": {"description": "OS crash data.", "id": "GoogleChromeManagementV1TelemetryOsCrashEvent", "properties": {"crashId": {"description": "Crash id.", "type": "string"}, "crashType": {"description": "Crash type.", "enum": ["CRASH_TYPE_UNSPECIFIED", "CRASH_TYPE_KERNEL", "CRASH_TYPE_EMBEDDED_CONTROLLER"], "enumDescriptions": ["Crash type unknown.", "Kernel crash.", "Embedded controller crash."], "type": "string"}, "sessionType": {"description": "Session type.", "enum": ["SESSION_TYPE_UNSPECIFIED", "SESSION_TYPE_SIGNED_IN_USER", "SESSION_TYPE_KIOSK", "SESSION_TYPE_MANAGED_GUEST", "SESSION_TYPE_ACTIVE_DIRECTORY"], "enumDescriptions": ["Session type unknown.", "Signed in user.", "Kiosk.", "Managed guest session.", "Active directory session."], "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1TelemetryUsbPeripheralsEvent": {"description": "`TelemetryUsbPeripheralsEvent` is triggered USB devices are either added or removed. * Granular permission needed: TELEMETRY_API_PERIPHERALS_REPORT", "id": "GoogleChromeManagementV1TelemetryUsbPeripheralsEvent", "properties": {"usbPeripheralReport": {"description": "List of usb devices that were either added or removed.", "items": {"$ref": "GoogleChromeManagementV1UsbPeripheralReport"}, "type": "array"}}, "type": "object"}, "GoogleChromeManagementV1TelemetryUser": {"description": "Telemetry data collected from a managed user. * Granular permission needed: TELEMETRY_API_USER", "id": "GoogleChromeManagementV1TelemetryUser", "properties": {"customer": {"description": "G Suite Customer whose enterprise enrolled the device.", "type": "string"}, "name": {"description": "Resource name of the user.", "type": "string"}, "orgUnitId": {"description": "Organization unit of the user.", "type": "string"}, "userDevice": {"description": "Telemetry data collected from a managed user and device.", "items": {"$ref": "GoogleChromeManagementV1TelemetryUserDevice"}, "type": "array"}, "userEmail": {"description": "Email address of the user.", "type": "string"}, "userId": {"description": "Directory ID of the user.", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1TelemetryUserDevice": {"description": "Telemetry data collected for a managed user and device. * Granular permission needed: TELEMETRY_API_DEVICE", "id": "GoogleChromeManagementV1TelemetryUserDevice", "properties": {"appReport": {"description": "Output only. App reports collected periodically sorted in a decreasing order of report_time.", "items": {"$ref": "GoogleChromeManagementV1AppReport"}, "readOnly": true, "type": "array"}, "audioStatusReport": {"description": "Output only. Audio reports collected periodically sorted in a decreasing order of report_time.", "items": {"$ref": "GoogleChromeManagementV1AudioStatusReport"}, "readOnly": true, "type": "array"}, "deviceActivityReport": {"description": "Output only. Device activity reports collected periodically sorted in a decreasing order of report_time.", "items": {"$ref": "GoogleChromeManagementV1DeviceActivityReport"}, "readOnly": true, "type": "array"}, "deviceId": {"description": "The unique Directory API ID of the device. This value is the same as the Admin Console's Directory API ID in the ChromeOS Devices tab.", "type": "string"}, "networkBandwidthReport": {"description": "Output only. Network bandwidth reports collected periodically sorted in a decreasing order of report_time.", "items": {"$ref": "GoogleChromeManagementV1NetworkBandwidthReport"}, "readOnly": true, "type": "array"}, "peripheralsReport": {"description": "Output only. Peripherals reports collected periodically sorted in a decreasing order of report_time.", "items": {"$ref": "GoogleChromeManagementV1PeripheralsReport"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleChromeManagementV1TelemetryUserInfo": {"description": "Information about a user associated with telemetry data. * Granular permission needed: TELEMETRY_API_USER", "id": "GoogleChromeManagementV1TelemetryUserInfo", "properties": {"email": {"description": "Output only. User's email.", "readOnly": true, "type": "string"}, "orgUnitId": {"description": "Output only. Organization unit ID of the user.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1ThunderboltInfo": {"description": "Thunderbolt bus info. * This field provides device information, which is static and will not change over time. * Data for this field is controlled via policy: [ReportDeviceSecurityStatus](https://chromeenterprise.google/policies/#ReportDeviceSecurityStatus) * Data Collection Frequency: At device startup * Default Data Reporting Frequency: At device startup - Policy Controlled: No * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: Yes * Reported for affiliated users only: N/A * Granular permission needed: TELEMETRY_API_BUS_DEVICE_INFO", "id": "GoogleChromeManagementV1ThunderboltInfo", "properties": {"securityLevel": {"description": "Security level of the Thunderbolt bus.", "enum": ["THUNDERBOLT_SECURITY_LEVEL_UNSPECIFIED", "THUNDERBOLT_SECURITY_NONE_LEVEL", "THUNDERBOLT_SECURITY_USER_LEVEL", "THUNDERBOLT_SECURITY_SECURE_LEVEL", "THUNDERBOLT_SECURITY_DP_ONLY_LEVEL", "THUNDERBOLT_SECURITY_USB_ONLY_LEVEL", "THUNDERBOLT_SECURITY_NO_PCIE_LEVEL"], "enumDescriptions": ["Thunderbolt security level is not set.", "All devices are automatically connected by the firmware. No user approval is needed.", "User is asked whether the device is allowed to be connected.", "User is asked whether the device is allowed to be connected. In addition the device is sent a challenge that should match the expected one based on a random key written to the key sysfs attribute", "The firmware automatically creates tunnels for Thunderbolt.", "The firmware automatically creates tunnels for the USB controller and Display Port in a dock. All PCIe links downstream of the dock are removed.", "PCIE tunneling is disabled."], "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1TotalMemoryEncryptionInfo": {"description": "Memory encryption information of a device. * This field provides device information, which is static and will not change over time. * Data for this field is controlled via policy: [ReportDeviceMemoryInfo](https://chromeenterprise.google/policies/#ReportDeviceMemoryInfo) * Data Collection Frequency: At device startup * Default Data Reporting Frequency: At device startup - Policy Controlled: Yes * Cache: If the device is offline, the collected data is stored locally, and will be reported when the device is next online: Yes * Reported for affiliated users only: N/A", "id": "GoogleChromeManagementV1TotalMemoryEncryptionInfo", "properties": {"encryptionAlgorithm": {"description": "Memory encryption algorithm.", "enum": ["MEMORY_ENCRYPTION_ALGORITHM_UNSPECIFIED", "MEMORY_ENCRYPTION_ALGORITHM_UNKNOWN", "MEMORY_ENCRYPTION_ALGORITHM_AES_XTS_128", "MEMORY_ENCRYPTION_ALGORITHM_AES_XTS_256"], "enumDescriptions": ["Memory encryption algorithm is not set.", "The memory encryption algorithm being used is unknown.", "The memory encryption algorithm is using the AES_XTS encryption algorithm with a 128 bit block cypher.", "The memory encryption algorithm is using the AES_XTS encryption algorithm with a 256 bit block cypher."], "type": "string"}, "encryptionState": {"description": "The state of memory encryption on the device.", "enum": ["MEMORY_ENCRYPTION_STATE_UNSPECIFIED", "MEMORY_ENCRYPTION_STATE_UNKNOWN", "MEMORY_ENCRYPTION_STATE_DISABLED", "MEMORY_ENCRYPTION_STATE_TME", "MEMORY_ENCRYPTION_STATE_MKTME"], "enumDescriptions": ["Memory encryption state is not set.", "The memory encryption state is unknown.", "Memory encrpytion on the device is disabled.", "Memory encryption on the device uses total memory encryption.", "Memory encryption on the device uses multi-key total memory encryption."], "type": "string"}, "keyLength": {"description": "The length of the encryption keys.", "format": "int64", "type": "string"}, "maxKeys": {"description": "The maximum number of keys that can be used for encryption.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1TouchScreenDevice": {"description": "Information of an internal touch screen device.", "id": "GoogleChromeManagementV1TouchScreenDevice", "properties": {"displayName": {"description": "Output only. Touch screen device display name.", "readOnly": true, "type": "string"}, "stylusCapable": {"description": "Output only. Touch screen device is stylus capable or not.", "readOnly": true, "type": "boolean"}, "touchPointCount": {"description": "Output only. Number of touch points supported on the device.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "GoogleChromeManagementV1TouchScreenInfo": {"description": "Information on the device touch screen.", "id": "GoogleChromeManagementV1TouchScreenInfo", "properties": {"devices": {"description": "Output only. List of the internal touch screen devices.", "items": {"$ref": "GoogleChromeManagementV1TouchScreenDevice"}, "readOnly": true, "type": "array"}, "touchpadLibrary": {"description": "Output only. Touchpad library name used by the input stack.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1UsbPeripheralReport": {"description": "USB connected peripheral report.", "id": "GoogleChromeManagementV1UsbPeripheralReport", "properties": {"categories": {"description": "Output only. Categories the device belongs to https://www.usb.org/defined-class-codes", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "classId": {"description": "Output only. Class ID https://www.usb.org/defined-class-codes", "format": "int32", "readOnly": true, "type": "integer"}, "firmwareVersion": {"description": "Output only. Firmware version", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Device name, model name, or product name", "readOnly": true, "type": "string"}, "pid": {"description": "Output only. Product ID", "format": "int32", "readOnly": true, "type": "integer"}, "subclassId": {"description": "Output only. Subclass ID https://www.usb.org/defined-class-codes", "format": "int32", "readOnly": true, "type": "integer"}, "vendor": {"description": "Output only. Vendor name", "readOnly": true, "type": "string"}, "vid": {"description": "Output only. Vendor ID", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "GoogleChromeManagementV1UserPrintReport": {"description": "Report for CountPrintJobsByUser, contains printing statistics for a user. Contains the number of printers, the number of devices used to initiate print jobs, and the number of print jobs initiated.", "id": "GoogleChromeManagementV1UserPrintReport", "properties": {"deviceCount": {"description": "Number of chrome devices that have been used to initiate print jobs by the user.", "format": "int64", "type": "string"}, "jobCount": {"description": "Number of print jobs initiated by the user.", "format": "int64", "type": "string"}, "printerCount": {"description": "Number of printers used by the user.", "format": "int64", "type": "string"}, "userEmail": {"description": "The primary e-mail address of the user.", "type": "string"}, "userId": {"description": "The unique Directory API ID of the user.", "type": "string"}}, "type": "object"}, "GoogleChromeManagementV1UserRequestingExtensionDetails": {"description": "Details of a user requesting an extension, including the email and the justification.", "id": "GoogleChromeManagementV1UserRequestingExtensionDetails", "properties": {"email": {"description": "The e-mail address of a user that has requested the extension.", "type": "string"}, "justification": {"description": "Request justification as entered by the user.", "type": "string"}}, "type": "object"}, "GoogleChromeManagementVersionsV1AttestationCredential": {"description": "Information of public key associated with a Chrome browser profile.", "id": "GoogleChromeManagementVersionsV1AttestationCredential", "properties": {"keyRotationTime": {"description": "Output only. Latest rotation timestamp of the public key rotation.", "format": "google-datetime", "readOnly": true, "type": "string"}, "keyTrustLevel": {"description": "Output only. Trust level of the public key.", "enum": ["KEY_TRUST_LEVEL_UNSPECIFIED", "CHROME_BROWSER_HW_KEY", "CHROME_BROWSER_OS_KEY"], "enumDescriptions": ["Represents an unspecified public key trust level.", "Represents a HW key.", "Represents an OS key."], "readOnly": true, "type": "string"}, "keyType": {"description": "Output only. Type of the public key.", "enum": ["KEY_TYPE_UNSPECIFIED", "RSA_KEY", "EC_KEY"], "enumDescriptions": ["Represents an unspecified public key type.", "Represents a RSA key.", "Represents an EC key."], "readOnly": true, "type": "string"}, "publicKey": {"description": "Output only. Value of the public key.", "format": "byte", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementVersionsV1CertificateProvisioningProcess": {"description": "A certificate provisioning process.", "id": "GoogleChromeManagementVersionsV1CertificateProvisioningProcess", "properties": {"chromeOsDevice": {"$ref": "GoogleChromeManagementVersionsV1ChromeOsDevice", "description": "Output only. The client certificate is being provisioned for a ChromeOS device. This contains information about the device.", "readOnly": true}, "chromeOsUserSession": {"$ref": "GoogleChromeManagementVersionsV1ChromeOsUserSession", "description": "Output only. The client certificate is being provisioned for a ChromeOS user. This contains information about the current user session.", "readOnly": true}, "failureMessage": {"description": "Output only. A message describing why this `CertificateProvisioningProcess` has failed. Presence of this field indicates that the `CertificateProvisioningProcess` has failed.", "readOnly": true, "type": "string"}, "genericCaConnection": {"$ref": "GoogleChromeManagementVersionsV1GenericCaConnection", "description": "Output only. The CA connection is a generic CA connection.", "readOnly": true}, "genericProfile": {"$ref": "GoogleChromeManagementVersionsV1GenericProfile", "description": "Output only. The profile is a generic certificate provisioning profile.", "readOnly": true}, "issuedCertificate": {"description": "Output only. The issued certificate for this `CertificateProvisioningProcess` in PEM format.", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. Resource name of the `CertificateProvisioningProcess`. The name pattern is given as `customers/{customer}/certificateProvisioningProcesses/{certificate_provisioning_process}` with `{customer}` being the obfuscated customer id and `{certificate_provisioning_process}` being the certificate provisioning process id.", "type": "string"}, "provisioningProfileId": {"description": "Output only. The ID of the certificate provisioning profile.", "readOnly": true, "type": "string"}, "signData": {"description": "Output only. The data that the client was asked to sign. This field is only present after the `SignData` operation has been initiated.", "format": "byte", "readOnly": true, "type": "string"}, "signature": {"description": "Output only. The signature of `signature_algorithm`, generated using the client's private key using `signature_algorithm`. This field is only present after the `SignData` operation has finished.", "format": "byte", "readOnly": true, "type": "string"}, "signatureAlgorithm": {"description": "Output only. The signature algorithm that the adapter expects the client and backend components to use when processing `sign_data`. This field is only present after the `SignData` operation has been initiated.", "enum": ["SIGNATURE_ALGORITHM_UNSPECIFIED", "SIGNATURE_ALGORITHM_RSA_PKCS1_V1_5_SHA256"], "enumDescriptions": ["Default value. This value is unused.", "The server-side builds the PKCS#1 DigestInfo, i.e., the SHA256 hash is constructed on the server-side. The client should sign using RSA with PKCS#1 v1.5 padding."], "readOnly": true, "type": "string"}, "startTime": {"description": "Output only. Server-generated timestamp of when the certificate provisioning process has been created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "subjectPublicKeyInfo": {"description": "Output only. The public key for which a certificate should be provisioned. Represented as a DER-encoded X.509 SubjectPublicKeyInfo.", "format": "byte", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementVersionsV1ChromeBrowserProfile": {"description": "A representation of a Chrome browser profile.", "id": "GoogleChromeManagementVersionsV1ChromeBrowserProfile", "properties": {"affiliationState": {"description": "Output only. The specific affiliation state of the profile.", "enum": ["AFFILIATION_STATE_UNSPECIFIED", "UNAFFILIATED_GENERIC", "PROFILE_ONLY", "UNAFFILIATED_LOCAL_MACHINE", "UNAFFILIATED_CLOUD_MACHINE", "AFFILIATED_CLOUD_MANAGED"], "enumDescriptions": ["Unspecified affiliation state.", "Unaffiliated - but we do not have the details for the type of unaffiliated profile.", "Unaffiliated - A managed profile that appears on a totally unamanaged browser.", "Unaffiliated - A managed profile that appears on a machine that is locally managed by a different organization (through platform management mechanisms like GPO).", "Unaffiliated - A managed profile that appears on a managed browser that is cloud managed by a different organization (using Chrome Browser Cloud Management).", "Affiliated - Both the profile and the managed browser are managed by the same organization."], "readOnly": true, "type": "string"}, "annotatedLocation": {"description": "Optional. Location of the profile annotated by the admin.", "type": "string"}, "annotatedUser": {"description": "Optional. User of the profile annotated by the admin.", "type": "string"}, "attestationCredential": {"$ref": "GoogleChromeManagementVersionsV1AttestationCredential", "description": "Output only. Attestation credential information of the profile.", "readOnly": true}, "browserChannel": {"description": "Output only. Channel of the browser on which the profile exists.", "readOnly": true, "type": "string"}, "browserVersion": {"description": "Output only. Version of the browser on which the profile exists.", "readOnly": true, "type": "string"}, "deviceInfo": {"$ref": "GoogleChromeManagementVersionsV1DeviceInfo", "description": "Output only. Basic information of the device on which the profile exists. This information is only available for the affiliated profiles.", "readOnly": true}, "displayName": {"description": "Output only. Profile display name set by client.", "readOnly": true, "type": "string"}, "etag": {"description": "Output only. Etag of this ChromeBrowserProfile resource. This etag can be used with UPDATE operation to ensure consistency.", "readOnly": true, "type": "string"}, "extensionCount": {"description": "Output only. Number of extensions installed on the profile.", "format": "int64", "readOnly": true, "type": "string"}, "firstEnrollmentTime": {"description": "Output only. Timestamp of the first enrollment of the profile.", "format": "google-datetime", "readOnly": true, "type": "string"}, "identityProvider": {"description": "Output only. Identify provider of the profile.", "enum": ["IDENTITY_PROVIDER_UNSPECIFIED", "GOOGLE_IDENTITY_PROVIDER", "EXTERNAL_IDENTITY_PROVIDER"], "enumDescriptions": ["Represents an unspecified identity provider.", "Represents a Google identity provider.", "Represents an external identity provider."], "readOnly": true, "type": "string"}, "lastActivityTime": {"description": "Output only. Timestamp of the latest activity by the profile.", "format": "google-datetime", "readOnly": true, "type": "string"}, "lastPolicyFetchTime": {"description": "Output only. Timestamp of the latest policy fetch by the profile.", "format": "google-datetime", "readOnly": true, "type": "string"}, "lastPolicySyncTime": {"description": "Output only. Timestamp of the latest policy sync by the profile.", "format": "google-datetime", "readOnly": true, "type": "string"}, "lastStatusReportTime": {"description": "Output only. Timestamp of the latest status report by the profile.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. Format: customers/{customer_id}/profiles/{profile_permanent_id}", "type": "string"}, "osPlatformType": {"description": "Output only. OS platform of the device on which the profile exists.", "readOnly": true, "type": "string"}, "osPlatformVersion": {"description": "Output only. Major OS platform version of the device on which the profile exists, from profile reporting.", "readOnly": true, "type": "string"}, "osVersion": {"description": "Output only. OS version of the device on which the profile exists.", "readOnly": true, "type": "string"}, "policyCount": {"description": "Output only. Number of policies applied on the profile.", "format": "int64", "readOnly": true, "type": "string"}, "profileId": {"description": "Output only. Chrome client side profile ID.", "readOnly": true, "type": "string"}, "profilePermanentId": {"description": "Output only. Profile permanent ID is the unique identifier of a profile within one customer.", "readOnly": true, "type": "string"}, "reportingData": {"$ref": "GoogleChromeManagementVersionsV1ReportingData", "description": "Output only. Detailed reporting data of the profile. This information is only available when the profile reporting policy is enabled.", "readOnly": true}, "userEmail": {"description": "Output only. Email address of the user to which the profile belongs.", "readOnly": true, "type": "string"}, "userId": {"description": "Output only. Unique Directory API ID of the user that can be used in Admin SDK Users API.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementVersionsV1ChromeOsDevice": {"description": "Describes the ChromeOS device that a `CertificateProvisioningProcess` belongs to.", "id": "GoogleChromeManagementVersionsV1ChromeOsDevice", "properties": {"deviceDirectoryApiId": {"description": "Output only. The unique Directory API ID of the device. This value is the same as the Admin Console's Directory API ID in the ChromeOS Devices tab.", "readOnly": true, "type": "string"}, "serialNumber": {"description": "Output only. Device serial number. This value is the same as the Admin Console's Serial Number in the ChromeOS Devices tab.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementVersionsV1ChromeOsUserSession": {"description": "Describes the ChromeOS user session that a `CertificateProvisioningProcess` belongs to.", "id": "GoogleChromeManagementVersionsV1ChromeOsUserSession", "properties": {"chromeOsDevice": {"$ref": "GoogleChromeManagementVersionsV1ChromeOsDevice", "description": "Output only. This field contains information about the ChromeOS device that the user session is running on. It is only set if the user is affiliated, i.e., if the user is managed by the same organization that manages the ChromeOS device.", "readOnly": true}, "userDirectoryApiId": {"description": "Output only. The unique Directory API ID of the user.", "readOnly": true, "type": "string"}, "userPrimaryEmail": {"description": "Output only. The primary e-mail address of the user.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementVersionsV1DeviceInfo": {"description": "Information of a device that runs a Chrome browser profile.", "id": "GoogleChromeManagementVersionsV1DeviceInfo", "properties": {"affiliatedDeviceId": {"description": "Output only. Device ID that identifies the affiliated device on which the profile exists. If the device type is CHROME_BROWSER, then this represents a unique Directory API ID of the device that can be used in Admin SDK Browsers API.", "readOnly": true, "type": "string"}, "deviceType": {"description": "Output only. Type of the device on which the profile exists.", "enum": ["DEVICE_TYPE_UNSPECIFIED", "CHROME_BROWSER"], "enumDescriptions": ["Represents an unspecified device type.", "Represents a Chrome browser device."], "readOnly": true, "type": "string"}, "hostname": {"description": "Output only. Hostname of the device on which the profile exists.", "readOnly": true, "type": "string"}, "machine": {"description": "Output only. Machine name of the device on which the profile exists. On platforms which do not report the machine name (currently iOS and Android) this is instead set to the browser's device_id - but note that this is a different device_id than the |affiliated_device_id|.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementVersionsV1GenericCaConnection": {"description": "Describes a generic Certificate Authority Connection.", "id": "GoogleChromeManagementVersionsV1GenericCaConnection", "properties": {"caConnectionAdapterConfigReference": {"description": "Output only. A string that references the administrator-provided configuration for the certification authority service. This field can be missing if no configuration was given.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementVersionsV1GenericProfile": {"description": "Describes a generic certificate provisioning profile.", "id": "GoogleChromeManagementVersionsV1GenericProfile", "properties": {"profileAdapterConfigReference": {"description": "Output only. A string that references the administrator-provided configuration for the certificate provisioning profile. This field can be missing if no configuration was given.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementVersionsV1ListChromeBrowserProfilesResponse": {"description": "Response to ListChromeBrowserProfiles method.", "id": "GoogleChromeManagementVersionsV1ListChromeBrowserProfilesResponse", "properties": {"chromeBrowserProfiles": {"description": "The list of profiles returned.", "items": {"$ref": "GoogleChromeManagementVersionsV1ChromeBrowserProfile"}, "type": "array"}, "nextPageToken": {"description": "The pagination token that can be used to list the next page.", "type": "string"}, "totalSize": {"description": "Total size represents an estimated number of resources returned. Not guaranteed to be accurate above 10k profiles.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleChromeManagementVersionsV1ReportingData": {"description": "Reporting data of a Chrome browser profile.", "id": "GoogleChromeManagementVersionsV1ReportingData", "properties": {"browserExecutablePath": {"description": "Output only. Executable path of the installed Chrome browser. A valid path is included only in affiliated profiles.", "readOnly": true, "type": "string"}, "extensionData": {"description": "Output only. Information of the extensions installed on the profile.", "items": {"$ref": "GoogleChromeManagementVersionsV1ReportingDataExtensionData"}, "readOnly": true, "type": "array"}, "extensionPolicyData": {"description": "Output only. Information of the policies applied on the extensions.", "items": {"$ref": "GoogleChromeManagementVersionsV1ReportingDataExtensionPolicyData"}, "readOnly": true, "type": "array"}, "installedBrowserVersion": {"description": "Output only. Updated version of a browser, if it is different from the active browser version.", "readOnly": true, "type": "string"}, "policyData": {"description": "Output only. Information of the policies applied on the profile.", "items": {"$ref": "GoogleChromeManagementVersionsV1ReportingDataPolicyData"}, "readOnly": true, "type": "array"}, "profilePath": {"description": "Output only. Path of the profile. A valid path is included only in affiliated profiles.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementVersionsV1ReportingDataConflictingPolicyData": {"description": "Information of conflicting policy applied on a Chrome browser profile.", "id": "GoogleChromeManagementVersionsV1ReportingDataConflictingPolicyData", "properties": {"source": {"description": "Output only. Source of the policy.", "enum": ["POLICY_SOURCE_UNSPECIFIED", "MACHINE_PLATFORM", "USER_PLATFORM", "MACHINE_LEVEL_USER_CLOUD", "USER_CLOUD", "MACHINE_MERGED"], "enumDescriptions": ["Represents an unspecified policy source.", "Represents a machine level platform policy.", "Represents a user level platform policy.", "Represents a machine level user cloud policy.", "Represents a user level cloud policy.", "Represents a machine level merged policy."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementVersionsV1ReportingDataExtensionData": {"description": "Information of an extension installed on a Chrome browser profile.", "id": "GoogleChromeManagementVersionsV1ReportingDataExtensionData", "properties": {"description": {"description": "Output only. Description of the extension.", "readOnly": true, "type": "string"}, "extensionId": {"description": "Output only. ID of the extension.", "readOnly": true, "type": "string"}, "extensionType": {"description": "Output only. Type of the extension.", "enum": ["EXTENSION_TYPE_UNSPECIFIED", "EXTENSION", "APP", "THEME", "HOSTED_APP"], "enumDescriptions": ["Represents an unspecified extension type.", "Represents an extension.", "Represents an app.", "Represents a theme.", "Represents a hosted app."], "readOnly": true, "type": "string"}, "homepageUri": {"description": "Output only. The URL of the homepage of the extension.", "readOnly": true, "type": "string"}, "installationType": {"description": "Output only. Installation type of the extension.", "enum": ["INSTALLATION_TYPE_UNSPECIFIED", "MULTIPLE", "NORMAL", "ADMIN", "DEVELOPMENT", "SIDELOAD", "OTHER"], "enumDescriptions": ["Represents an unspecified installation type.", "Represents instances of the extension having mixed installation types.", "Represents a normal installation type.", "Represents an installation by admin.", "Represents a development installation type.", "Represents a sideload installation type.", "Represents an installation type that is not covered in the other options."], "readOnly": true, "type": "string"}, "isDisabled": {"description": "Output only. Represents whether the user disabled the extension.", "readOnly": true, "type": "boolean"}, "isWebstoreExtension": {"description": "Output only. Represents whether the extension is from the webstore.", "readOnly": true, "type": "boolean"}, "manifestVersion": {"description": "Output only. Manifest version of the extension.", "format": "int32", "readOnly": true, "type": "integer"}, "name": {"description": "Output only. Name of the extension.", "readOnly": true, "type": "string"}, "permissions": {"description": "Output only. Permissions requested by the extension.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "version": {"description": "Output only. Version of the extension.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementVersionsV1ReportingDataExtensionPolicyData": {"description": "Information of the policies applied on an extension.", "id": "GoogleChromeManagementVersionsV1ReportingDataExtensionPolicyData", "properties": {"extensionId": {"description": "Output only. ID of the extension.", "readOnly": true, "type": "string"}, "extensionName": {"description": "Output only. Name of the extension.", "readOnly": true, "type": "string"}, "policyData": {"description": "Output only. Information of the policies applied on the extension.", "items": {"$ref": "GoogleChromeManagementVersionsV1ReportingDataPolicyData"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleChromeManagementVersionsV1ReportingDataPolicyData": {"description": "Information of a policy applied on a Chrome browser profile.", "id": "GoogleChromeManagementVersionsV1ReportingDataPolicyData", "properties": {"conflicts": {"description": "Output only. Conflicting policy information.", "items": {"$ref": "GoogleChromeManagementVersionsV1ReportingDataConflictingPolicyData"}, "readOnly": true, "type": "array"}, "error": {"description": "Output only. Error message of the policy, if any.", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Name of the policy.", "readOnly": true, "type": "string"}, "source": {"description": "Output only. Source of the policy.", "enum": ["POLICY_SOURCE_UNSPECIFIED", "MACHINE_PLATFORM", "USER_PLATFORM", "MACHINE_LEVEL_USER_CLOUD", "USER_CLOUD", "MACHINE_MERGED"], "enumDescriptions": ["Represents an unspecified policy source.", "Represents a machine level platform policy.", "Represents a user level platform policy.", "Represents a machine level user cloud policy.", "Represents a user level cloud policy.", "Represents a machine level merged policy."], "readOnly": true, "type": "string"}, "value": {"description": "Output only. Value of the policy.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementVersionsV1SignDataMetadata": {"description": "Metadata for the long-running operation returned by signData.", "id": "GoogleChromeManagementVersionsV1SignDataMetadata", "properties": {"startTime": {"description": "Output only. Start time of the SignData operation.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChromeManagementVersionsV1SignDataResponse": {"description": "Response message for requesting a signature from the client that initated a certificate provisioning process.", "id": "GoogleChromeManagementVersionsV1SignDataResponse", "properties": {"certificateProvisioningProcess": {"$ref": "GoogleChromeManagementVersionsV1CertificateProvisioningProcess", "description": "Output only. The certificate provisioning process. The signature generated by the client will be available in the `signature` field of `CertificateProvisioningProcess`.", "readOnly": true}}, "type": "object"}, "GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}, "GoogleRpcStatus": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "GoogleRpcStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "GoogleTypeDate": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp", "id": "GoogleTypeDate", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}}, "servicePath": "", "title": "Chrome Management API", "version": "v1", "version_module": true}