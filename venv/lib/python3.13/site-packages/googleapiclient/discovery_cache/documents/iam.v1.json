{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://iam.googleapis.com/", "batchPath": "batch", "canonicalName": "<PERSON>am", "description": "Manages identity and access control for Google Cloud resources, including the creation of service accounts, which you can use to authenticate to Google and make API calls. Enabling this API also enables the IAM Service Account Credentials API (iamcredentials.googleapis.com). However, disabling this API doesn't disable the IAM Service Account Credentials API. ", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/iam/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "iam:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://iam.mtls.googleapis.com/", "name": "iam", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"iamPolicies": {"methods": {"lintPolicy": {"description": "Lints, or validates, an IAM policy. Currently checks the google.iam.v1.Binding.condition field, which contains a condition expression for a role binding. Successful calls to this method always return an HTTP `200 OK` status code, even if the linter detects an issue in the IAM policy.", "flatPath": "v1/iamPolicies:lintPolicy", "httpMethod": "POST", "id": "iam.iamPolicies.lintPolicy", "parameterOrder": [], "parameters": {}, "path": "v1/iamPolicies:lintPolicy", "request": {"$ref": "LintPolicyRequest"}, "response": {"$ref": "LintPolicyResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "queryAuditableServices": {"description": "Returns a list of services that allow you to opt into audit logs that are not generated by default. To learn more about audit logs, see the [Logging documentation](https://cloud.google.com/logging/docs/audit).", "flatPath": "v1/iamPolicies:queryAuditableServices", "httpMethod": "POST", "id": "iam.iamPolicies.queryAuditableServices", "parameterOrder": [], "parameters": {}, "path": "v1/iamPolicies:queryAuditableServices", "request": {"$ref": "QueryAuditableServicesRequest"}, "response": {"$ref": "QueryAuditableServicesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "locations": {"resources": {"workforcePools": {"methods": {"create": {"description": "Creates a new WorkforcePool. You cannot reuse the name of a deleted pool until 30 days after deletion.", "flatPath": "v1/locations/{locationsId}/workforcePools", "httpMethod": "POST", "id": "iam.locations.workforcePools.create", "parameterOrder": ["location"], "parameters": {"location": {"description": "Optional. The location of the pool to create. Format: `locations/{location}`.", "location": "path", "pattern": "^locations/[^/]+$", "required": true, "type": "string"}, "workforcePoolId": {"description": "Optional. The ID to use for the pool, which becomes the final component of the resource name. The IDs must be a globally unique string of 6 to 63 lowercase letters, digits, or hyphens. It must start with a letter, and cannot have a trailing hyphen. The prefix `gcp-` is reserved for use by Google, and may not be specified.", "location": "query", "type": "string"}}, "path": "v1/{+location}/workforcePools", "request": {"$ref": "WorkforcePool"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a WorkforcePool. You cannot use a deleted WorkforcePool to exchange external credentials for Google Cloud credentials. However, deletion does not revoke credentials that have already been issued. Credentials issued for a deleted pool do not grant access to resources. If the pool is undeleted, and the credentials are not expired, they grant access again. You can undelete a pool for 30 days. After 30 days, deletion is permanent. You cannot update deleted pools. However, you can view and list them.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}", "httpMethod": "DELETE", "id": "iam.locations.workforcePools.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the pool to delete. Format: `locations/{location}/workforcePools/{workforce_pool_id}`", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an individual WorkforcePool.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}", "httpMethod": "GET", "id": "iam.locations.workforcePools.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the pool to retrieve. Format: `locations/{location}/workforcePools/{workforce_pool_id}`", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "WorkforcePool"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets IAM policies on a WorkforcePool.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}:getIamPolicy", "httpMethod": "POST", "id": "iam.locations.workforcePools.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all non-deleted WorkforcePools under the specified parent. If `show_deleted` is set to `true`, then deleted pools are also listed.", "flatPath": "v1/locations/{locationsId}/workforcePools", "httpMethod": "GET", "id": "iam.locations.workforcePools.list", "parameterOrder": ["location"], "parameters": {"location": {"description": "The location of the pool. Format: `locations/{location}`.", "location": "path", "pattern": "^locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of pools to return. If unspecified, at most 50 pools will be returned. The maximum value is 1000; values above 1000 are truncated to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListWorkforcePools` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource to list pools for. Format: `organizations/{org-id}`.", "location": "query", "type": "string"}, "showDeleted": {"description": "Whether to return soft-deleted pools.", "location": "query", "type": "boolean"}}, "path": "v1/{+location}/workforcePools", "response": {"$ref": "ListWorkforcePoolsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing WorkforcePool.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}", "httpMethod": "PATCH", "id": "iam.locations.workforcePools.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the pool. Format: `locations/{location}/workforcePools/{workforce_pool_id}`", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "WorkforcePool"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets IAM policies on a WorkforcePool.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}:setIamPolicy", "httpMethod": "POST", "id": "iam.locations.workforcePools.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns the caller's permissions on the WorkforcePool. If the pool doesn't exist, this call returns an empty set of permissions. It doesn't return a `NOT_FOUND` error.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}:testIamPermissions", "httpMethod": "POST", "id": "iam.locations.workforcePools.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "undelete": {"description": "Undeletes a WorkforcePool, as long as it was deleted fewer than 30 days ago.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}:undelete", "httpMethod": "POST", "id": "iam.locations.workforcePools.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the pool to undelete. Format: `locations/{location}/workforcePools/{workforce_pool_id}`", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:undelete", "request": {"$ref": "UndeleteWorkforcePoolRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/operations/{operationsId}", "httpMethod": "GET", "id": "iam.locations.workforcePools.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "providers": {"methods": {"create": {"description": "Creates a new WorkforcePoolProvider in a WorkforcePool. You cannot reuse the name of a deleted provider until 30 days after deletion.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers", "httpMethod": "POST", "id": "iam.locations.workforcePools.providers.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The pool to create this provider in. Format: `locations/{location}/workforcePools/{workforce_pool_id}`", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+$", "required": true, "type": "string"}, "workforcePoolProviderId": {"description": "Required. The ID for the provider, which becomes the final component of the resource name. This value must be 4-32 characters, and may contain the characters [a-z0-9-]. The prefix `gcp-` is reserved for use by Google, and may not be specified.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/providers", "request": {"$ref": "WorkforcePoolProvider"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a WorkforcePoolProvider. Deleting a provider does not revoke credentials that have already been issued; they continue to grant access. You can undelete a provider for 30 days. After 30 days, deletion is permanent. You cannot update deleted providers. However, you can view and list them.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}", "httpMethod": "DELETE", "id": "iam.locations.workforcePools.providers.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the provider to delete. Format: `locations/{location}/workforcePools/{workforce_pool_id}/providers/{provider_id}`", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+/providers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an individual WorkforcePoolProvider.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}", "httpMethod": "GET", "id": "iam.locations.workforcePools.providers.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the provider to retrieve. Format: `locations/{location}/workforcePools/{workforce_pool_id}/providers/{provider_id}`", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+/providers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "WorkforcePoolProvider"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all non-deleted WorkforcePoolProviders in a WorkforcePool. If `show_deleted` is set to `true`, then deleted providers are also listed.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers", "httpMethod": "GET", "id": "iam.locations.workforcePools.providers.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of providers to return. If unspecified, at most 50 providers are returned. The maximum value is 100; values above 100 are truncated to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListWorkforcePoolProviders` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The pool to list providers for. Format: `locations/{location}/workforcePools/{workforce_pool_id}`", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Whether to return soft-deleted providers.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/providers", "response": {"$ref": "ListWorkforcePoolProvidersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing WorkforcePoolProvider.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}", "httpMethod": "PATCH", "id": "iam.locations.workforcePools.providers.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the provider. Format: `locations/{location}/workforcePools/{workforce_pool_id}/providers/{provider_id}`", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+/providers/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "WorkforcePoolProvider"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "undelete": {"description": "Undeletes a WorkforcePoolProvider, as long as it was deleted fewer than 30 days ago.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}:undelete", "httpMethod": "POST", "id": "iam.locations.workforcePools.providers.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the provider to undelete. Format: `locations/{location}/workforcePools/{workforce_pool_id}/providers/{provider_id}`", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+/providers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:undelete", "request": {"$ref": "UndeleteWorkforcePoolProviderRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"keys": {"methods": {"create": {"description": "Creates a new WorkforcePoolProviderKey in a WorkforcePoolProvider.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/keys", "httpMethod": "POST", "id": "iam.locations.workforcePools.providers.keys.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The provider to create this key in.", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+/providers/[^/]+$", "required": true, "type": "string"}, "workforcePoolProviderKeyId": {"description": "Required. The ID to use for the key, which becomes the final component of the resource name. This value must be 4-32 characters, and may contain the characters [a-z0-9-].", "location": "query", "type": "string"}}, "path": "v1/{+parent}/keys", "request": {"$ref": "WorkforcePoolProviderKey"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a WorkforcePoolProviderKey. You can undelete a key for 30 days. After 30 days, deletion is permanent.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/keys/{keysId}", "httpMethod": "DELETE", "id": "iam.locations.workforcePools.providers.keys.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the key to delete.", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+/providers/[^/]+/keys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a WorkforcePoolProviderKey.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/keys/{keysId}", "httpMethod": "GET", "id": "iam.locations.workforcePools.providers.keys.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the key to retrieve.", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+/providers/[^/]+/keys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "WorkforcePoolProviderKey"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all non-deleted WorkforcePoolProviderKeys in a WorkforcePoolProvider. If `show_deleted` is set to `true`, then deleted keys are also listed.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/keys", "httpMethod": "GET", "id": "iam.locations.workforcePools.providers.keys.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of keys to return. If unspecified, all keys are returned. The maximum value is 10; values above 10 are truncated to 10.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListWorkforcePoolProviderKeys` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The provider resource to list encryption keys for. Format: `locations/{location}/workforcePools/{workforce_pool_id}/providers/{provider_id}`", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+/providers/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Whether to return soft-deleted keys.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/keys", "response": {"$ref": "ListWorkforcePoolProviderKeysResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "undelete": {"description": "Undeletes a WorkforcePoolProviderKey, as long as it was deleted fewer than 30 days ago.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/keys/{keysId}:undelete", "httpMethod": "POST", "id": "iam.locations.workforcePools.providers.keys.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the key to undelete.", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+/providers/[^/]+/keys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:undelete", "request": {"$ref": "UndeleteWorkforcePoolProviderKeyRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/keys/{keysId}/operations/{operationsId}", "httpMethod": "GET", "id": "iam.locations.workforcePools.providers.keys.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+/providers/[^/]+/keys/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/operations/{operationsId}", "httpMethod": "GET", "id": "iam.locations.workforcePools.providers.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+/providers/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "subjects": {"methods": {"delete": {"description": "Deletes a WorkforcePoolSubject. Subject must not already be in a deleted state. A WorkforcePoolSubject is automatically created the first time an external credential is exchanged for a Google Cloud credential using a mapped `google.subject` attribute. There is no endpoint to manually create a WorkforcePoolSubject. For 30 days after a WorkforcePoolSubject is deleted, using the same `google.subject` attribute in token exchanges with Google Cloud STS fails. Call UndeleteWorkforcePoolSubject to undelete a WorkforcePoolSubject that has been deleted, within within 30 days of deleting it. After 30 days, the WorkforcePoolSubject is permanently deleted. At this point, a token exchange with Google Cloud STS that uses the same mapped `google.subject` attribute automatically creates a new WorkforcePoolSubject that is unrelated to the previously deleted WorkforcePoolSubject but has the same `google.subject` value.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/subjects/{subjectsId}", "httpMethod": "DELETE", "id": "iam.locations.workforcePools.subjects.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the WorkforcePoolSubject. Special characters, like `/` and `:`, must be escaped, because all URLs need to conform to the \"When to Escape and Unescape\" section of [RFC3986](https://www.ietf.org/rfc/rfc2396.txt). Format: `locations/{location}/workforcePools/{workforce_pool_id}/subjects/{subject_id}`", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+/subjects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "undelete": {"description": "Undeletes a WorkforcePoolSubject, as long as it was deleted fewer than 30 days ago.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/subjects/{subjectsId}:undelete", "httpMethod": "POST", "id": "iam.locations.workforcePools.subjects.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the WorkforcePoolSubject. Special characters, like `/` and `:`, must be escaped, because all URLs need to conform to the \"When to Escape and Unescape\" section of [RFC3986](https://www.ietf.org/rfc/rfc2396.txt). Format: `locations/{location}/workforcePools/{workforce_pool_id}/subjects/{subject_id}`", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+/subjects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:undelete", "request": {"$ref": "UndeleteWorkforcePoolSubjectRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/subjects/{subjectsId}/operations/{operationsId}", "httpMethod": "GET", "id": "iam.locations.workforcePools.subjects.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+/subjects/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}, "organizations": {"resources": {"roles": {"methods": {"create": {"description": "Creates a new custom Role.", "flatPath": "v1/organizations/{organizationsId}/roles", "httpMethod": "POST", "id": "iam.organizations.roles.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "The `parent` parameter's value depends on the target resource for the request, namely [projects](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles) or [organizations](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles). Each resource type's `parent` value format is described below: * [projects.roles.create](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles/create): `projects/{PROJECT_ID}`. This method creates project-level [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles). Example request URL: `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles` * [organizations.roles.create](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles/create): `organizations/{ORGANIZATION_ID}`. This method creates organization-level [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles). Example request URL: `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles` Note: Wildcard (*) values are invalid; you must specify a complete project ID or organization ID.", "location": "path", "pattern": "^organizations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/roles", "request": {"$ref": "CreateRoleRequest"}, "response": {"$ref": "Role"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a custom Role. When you delete a custom role, the following changes occur immediately: * You cannot bind a principal to the custom role in an IAM Policy. * Existing bindings to the custom role are not changed, but they have no effect. * By default, the response from ListRoles does not include the custom role. A deleted custom role still counts toward the [custom role limit](https://cloud.google.com/iam/help/limits) until it is permanently deleted. You have 7 days to undelete the custom role. After 7 days, the following changes occur: * The custom role is permanently deleted and cannot be recovered. * If an IAM policy contains a binding to the custom role, the binding is permanently removed. * The custom role no longer counts toward your custom role limit.", "flatPath": "v1/organizations/{organizationsId}/roles/{rolesId}", "httpMethod": "DELETE", "id": "iam.organizations.roles.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Used to perform a consistent read-modify-write.", "format": "byte", "location": "query", "type": "string"}, "name": {"description": "The `name` parameter's value depends on the target resource for the request, namely [projects](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles) or [organizations](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles). Each resource type's `name` value format is described below: * [projects.roles.delete](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles/delete): `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method deletes only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the project level. Example request URL: `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}` * [organizations.roles.delete](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles/delete): `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method deletes only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the organization level. Example request URL: `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a complete project ID or organization ID.", "location": "path", "pattern": "^organizations/[^/]+/roles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Role"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the definition of a Role.", "flatPath": "v1/organizations/{organizationsId}/roles/{rolesId}", "httpMethod": "GET", "id": "iam.organizations.roles.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The `name` parameter's value depends on the target resource for the request, namely [roles](https://cloud.google.com/iam/docs/reference/rest/v1/roles), [projects](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles), or [organizations](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles). Each resource type's `name` value format is described below: * [roles.get](https://cloud.google.com/iam/docs/reference/rest/v1/roles/get): `roles/{ROLE_NAME}`. This method returns results from all [predefined roles](https://cloud.google.com/iam/docs/understanding-roles#predefined_roles) in IAM. Example request URL: `https://iam.googleapis.com/v1/roles/{ROLE_NAME}` * [projects.roles.get](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles/get): `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method returns only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the project level. Example request URL: `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}` * [organizations.roles.get](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles/get): `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method returns only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the organization level. Example request URL: `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a complete project ID or organization ID.", "location": "path", "pattern": "^organizations/[^/]+/roles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Role"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists every predefined Role that IAM supports, or every custom role that is defined for an organization or project.", "flatPath": "v1/organizations/{organizationsId}/roles", "httpMethod": "GET", "id": "iam.organizations.roles.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional limit on the number of roles to include in the response. The default is 300, and the maximum is 1,000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional pagination token returned in an earlier ListRolesResponse.", "location": "query", "type": "string"}, "parent": {"description": "The `parent` parameter's value depends on the target resource for the request, namely [roles](https://cloud.google.com/iam/docs/reference/rest/v1/roles), [projects](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles), or [organizations](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles). Each resource type's `parent` value format is described below: * [roles.list](https://cloud.google.com/iam/docs/reference/rest/v1/roles/list): An empty string. This method doesn't require a resource; it simply returns all [predefined roles](https://cloud.google.com/iam/docs/understanding-roles#predefined_roles) in IAM. Example request URL: `https://iam.googleapis.com/v1/roles` * [projects.roles.list](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles/list): `projects/{PROJECT_ID}`. This method lists all project-level [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles). Example request URL: `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles` * [organizations.roles.list](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles/list): `organizations/{ORGANIZATION_ID}`. This method lists all organization-level [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles). Example request URL: `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles` Note: Wildcard (*) values are invalid; you must specify a complete project ID or organization ID.", "location": "path", "pattern": "^organizations/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Include Roles that have been deleted.", "location": "query", "type": "boolean"}, "view": {"description": "Optional view for the returned Role objects. When `FULL` is specified, the `includedPermissions` field is returned, which includes a list of all permissions in the role. The default value is `BASIC`, which does not return the `includedPermissions` field.", "enum": ["BASIC", "FULL"], "enumDescriptions": ["Omits the `included_permissions` field. This is the default value.", "Returns all fields."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/roles", "response": {"$ref": "ListRolesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the definition of a custom Role.", "flatPath": "v1/organizations/{organizationsId}/roles/{rolesId}", "httpMethod": "PATCH", "id": "iam.organizations.roles.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The `name` parameter's value depends on the target resource for the request, namely [projects](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles) or [organizations](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles). Each resource type's `name` value format is described below: * [projects.roles.patch](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles/patch): `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method updates only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the project level. Example request URL: `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}` * [organizations.roles.patch](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles/patch): `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method updates only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the organization level. Example request URL: `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a complete project ID or organization ID.", "location": "path", "pattern": "^organizations/[^/]+/roles/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "A mask describing which fields in the Role have changed.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Role"}, "response": {"$ref": "Role"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "undelete": {"description": "Undeletes a custom Role.", "flatPath": "v1/organizations/{organizationsId}/roles/{rolesId}:undelete", "httpMethod": "POST", "id": "iam.organizations.roles.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The `name` parameter's value depends on the target resource for the request, namely [projects](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles) or [organizations](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles). Each resource type's `name` value format is described below: * [projects.roles.undelete](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles/undelete): `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method undeletes only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the project level. Example request URL: `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}` * [organizations.roles.undelete](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles/undelete): `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method undeletes only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the organization level. Example request URL: `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a complete project ID or organization ID.", "location": "path", "pattern": "^organizations/[^/]+/roles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:undelete", "request": {"$ref": "UndeleteRoleRequest"}, "response": {"$ref": "Role"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "permissions": {"methods": {"queryTestablePermissions": {"description": "Lists every permission that you can test on a resource. A permission is testable if you can check whether a principal has that permission on the resource.", "flatPath": "v1/permissions:queryTestablePermissions", "httpMethod": "POST", "id": "iam.permissions.queryTestablePermissions", "parameterOrder": [], "parameters": {}, "path": "v1/permissions:queryTestablePermissions", "request": {"$ref": "QueryTestablePermissionsRequest"}, "response": {"$ref": "QueryTestablePermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "projects": {"resources": {"locations": {"resources": {"oauthClients": {"methods": {"create": {"description": "Creates a new OauthClient. You cannot reuse the name of a deleted OauthClient until 30 days after deletion.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/oauthClients", "httpMethod": "POST", "id": "iam.projects.locations.oauthClients.create", "parameterOrder": ["parent"], "parameters": {"oauthClientId": {"description": "Required. The ID to use for the OauthClient, which becomes the final component of the resource name. This value should be a string of 6 to 63 lowercase letters, digits, or hyphens. It must start with a letter, and cannot have a trailing hyphen. The prefix `gcp-` is reserved for use by Google, and may not be specified.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource to create the OauthClient in. The only supported location is `global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/oauthClients", "request": {"$ref": "OauthClient"}, "response": {"$ref": "OauthClient"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an OauthClient. You cannot use a deleted OauthClient. However, deletion does not revoke access tokens that have already been issued. They continue to grant access. Deletion does revoke refresh tokens that have already been issued. They cannot be used to renew an access token. If the OauthClient is undeleted, and the refresh tokens are not expired, they are valid for token exchange again. You can undelete an OauthClient for 30 days. After 30 days, deletion is permanent. You cannot update deleted OauthClients. However, you can view and list them.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}", "httpMethod": "DELETE", "id": "iam.projects.locations.oauthClients.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the OauthClient to delete. Format: `projects/{project}/locations/{location}/oauthClients/{oauth_client}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/oauthClients/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "OauthClient"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an individual OauthClient.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}", "httpMethod": "GET", "id": "iam.projects.locations.oauthClients.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the OauthClient to retrieve. Format: `projects/{project}/locations/{location}/oauthClients/{oauth_client}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/oauthClients/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "OauthClient"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all non-deleted OauthClients in a project. If `show_deleted` is set to `true`, then deleted OauthClients are also listed.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/oauthClients", "httpMethod": "GET", "id": "iam.projects.locations.oauthClients.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of OauthClients to return. If unspecified, at most 50 OauthClients will be returned. The maximum value is 100; values above 100 are truncated to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListOauthClients` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent to list OauthClients for.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Optional. Whether to return soft-deleted OauthClients.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/oauthClients", "response": {"$ref": "ListOauthClientsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing OauthClient.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}", "httpMethod": "PATCH", "id": "iam.projects.locations.oauthClients.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Identifier. The resource name of the OauthClient. Format:`projects/{project}/locations/{location}/oauthClients/{oauth_client}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/oauthClients/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "OauthClient"}, "response": {"$ref": "OauthClient"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "undelete": {"description": "Undeletes an OauthClient, as long as it was deleted fewer than 30 days ago.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}:undelete", "httpMethod": "POST", "id": "iam.projects.locations.oauthClients.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the OauthClient to undelete. Format: `projects/{project}/locations/{location}/oauthClients/{oauth_client}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/oauthClients/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:undelete", "request": {"$ref": "UndeleteOauthClientRequest"}, "response": {"$ref": "OauthClient"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"credentials": {"methods": {"create": {"description": "Creates a new OauthClientCredential.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}/credentials", "httpMethod": "POST", "id": "iam.projects.locations.oauthClients.credentials.create", "parameterOrder": ["parent"], "parameters": {"oauthClientCredentialId": {"description": "Required. The ID to use for the OauthClientCredential, which becomes the final component of the resource name. This value should be 4-32 characters, and may contain the characters [a-z0-9-]. The prefix `gcp-` is reserved for use by Google, and may not be specified.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource to create the OauthClientCredential in.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/oauthClients/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/credentials", "request": {"$ref": "OauthClientCredential"}, "response": {"$ref": "OauthClientCredential"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an OauthClientCredential. Before deleting an OauthClientCredential, it should first be disabled.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}/credentials/{credentialsId}", "httpMethod": "DELETE", "id": "iam.projects.locations.oauthClients.credentials.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the OauthClientCredential to delete. Format: `projects/{project}/locations/{location}/oauthClients/{oauth_client}/credentials/{credential}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/oauthClients/[^/]+/credentials/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an individual OauthClientCredential.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}/credentials/{credentialsId}", "httpMethod": "GET", "id": "iam.projects.locations.oauthClients.credentials.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the OauthClientCredential to retrieve. Format: `projects/{project}/locations/{location}/oauthClients/{oauth_client}/credentials/{credential}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/oauthClients/[^/]+/credentials/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "OauthClientCredential"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all OauthClientCredentials in an OauthClient.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}/credentials", "httpMethod": "GET", "id": "iam.projects.locations.oauthClients.credentials.list", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent to list OauthClientCredentials for.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/oauthClients/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/credentials", "response": {"$ref": "ListOauthClientCredentialsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing OauthClientCredential.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}/credentials/{credentialsId}", "httpMethod": "PATCH", "id": "iam.projects.locations.oauthClients.credentials.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Identifier. The resource name of the OauthClientCredential. Format: `projects/{project}/locations/{location}/oauthClients/{oauth_client}/credentials/{credential}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/oauthClients/[^/]+/credentials/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "OauthClientCredential"}, "response": {"$ref": "OauthClientCredential"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "workloadIdentityPools": {"methods": {"create": {"description": "Creates a new WorkloadIdentityPool. You cannot reuse the name of a deleted pool until 30 days after deletion.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools", "httpMethod": "POST", "id": "iam.projects.locations.workloadIdentityPools.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource to create the pool in. The only supported location is `global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "workloadIdentityPoolId": {"description": "Required. The ID to use for the pool, which becomes the final component of the resource name. This value should be 4-32 characters, and may contain the characters [a-z0-9-]. The prefix `gcp-` is reserved for use by Google, and may not be specified.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/workloadIdentityPools", "request": {"$ref": "WorkloadIdentityPool"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a WorkloadIdentityPool. You cannot use a deleted pool to exchange external credentials for Google Cloud credentials. However, deletion does not revoke credentials that have already been issued. Credentials issued for a deleted pool do not grant access to resources. If the pool is undeleted, and the credentials are not expired, they grant access again. You can undelete a pool for 30 days. After 30 days, deletion is permanent. You cannot update deleted pools. However, you can view and list them.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}", "httpMethod": "DELETE", "id": "iam.projects.locations.workloadIdentityPools.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the pool to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an individual WorkloadIdentityPool.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}", "httpMethod": "GET", "id": "iam.projects.locations.workloadIdentityPools.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the pool to retrieve.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "WorkloadIdentityPool"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the IAM policy of a WorkloadIdentityPool.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}:getIamPolicy", "httpMethod": "POST", "id": "iam.projects.locations.workloadIdentityPools.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all non-deleted WorkloadIdentityPools in a project. If `show_deleted` is set to `true`, then deleted pools are also listed.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools", "httpMethod": "GET", "id": "iam.projects.locations.workloadIdentityPools.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of pools to return. If unspecified, at most 50 pools are returned. The maximum value is 1000; values above are 1000 truncated to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListWorkloadIdentityPools` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource to list pools for.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Whether to return soft-deleted pools.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/workloadIdentityPools", "response": {"$ref": "ListWorkloadIdentityPoolsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing WorkloadIdentityPool.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}", "httpMethod": "PATCH", "id": "iam.projects.locations.workloadIdentityPools.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name of the pool.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "WorkloadIdentityPool"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the IAM policies on a WorkloadIdentityPool", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}:setIamPolicy", "httpMethod": "POST", "id": "iam.projects.locations.workloadIdentityPools.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns the caller's permissions on a WorkloadIdentityPool", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}:testIamPermissions", "httpMethod": "POST", "id": "iam.projects.locations.workloadIdentityPools.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "undelete": {"description": "Undeletes a WorkloadIdentityPool, as long as it was deleted fewer than 30 days ago.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}:undelete", "httpMethod": "POST", "id": "iam.projects.locations.workloadIdentityPools.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the pool to undelete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:undelete", "request": {"$ref": "UndeleteWorkloadIdentityPoolRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"namespaces": {"methods": {"create": {"description": "Creates a new WorkloadIdentityPoolNamespace in a WorkloadIdentityPool.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces", "httpMethod": "POST", "id": "iam.projects.locations.workloadIdentityPools.namespaces.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource to create the namespace in. The only supported location is `global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+$", "required": true, "type": "string"}, "workloadIdentityPoolNamespaceId": {"description": "Required. The ID to use for the namespace. This value must: * contain at most 63 characters * contain only lowercase alphanumeric characters or `-` * start with an alphanumeric character * end with an alphanumeric character The prefix \"gcp-\" will be reserved for future uses.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/namespaces", "request": {"$ref": "WorkloadIdentityPoolNamespace"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a WorkloadIdentityPoolNamespace. You can undelete a namespace for 30 days. After 30 days, deletion is permanent.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}", "httpMethod": "DELETE", "id": "iam.projects.locations.workloadIdentityPools.namespaces.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the namespace to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/namespaces/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an individual WorkloadIdentityPoolNamespace.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}", "httpMethod": "GET", "id": "iam.projects.locations.workloadIdentityPools.namespaces.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the namespace to retrieve.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/namespaces/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "WorkloadIdentityPoolNamespace"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all non-deleted WorkloadIdentityPoolNamespaces in a workload identity pool. If `show_deleted` is set to `true`, then deleted namespaces are also listed.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces", "httpMethod": "GET", "id": "iam.projects.locations.workloadIdentityPools.namespaces.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of namespaces to return. If unspecified, at most 50 namespaces are returned. The maximum value is 1000; values above are 1000 truncated to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListWorkloadIdentityPoolNamespaces` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource to list namespaces for.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Whether to return soft-deleted namespaces.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/namespaces", "response": {"$ref": "ListWorkloadIdentityPoolNamespacesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing WorkloadIdentityPoolNamespace in a WorkloadIdentityPool.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}", "httpMethod": "PATCH", "id": "iam.projects.locations.workloadIdentityPools.namespaces.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name of the namespace.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/namespaces/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "WorkloadIdentityPoolNamespace"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "undelete": {"description": "Undeletes a WorkloadIdentityPoolNamespace, as long as it was deleted fewer than 30 days ago.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}:undelete", "httpMethod": "POST", "id": "iam.projects.locations.workloadIdentityPools.namespaces.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the namespace to undelete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/namespaces/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:undelete", "request": {"$ref": "UndeleteWorkloadIdentityPoolNamespaceRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"managedIdentities": {"methods": {"addAttestationRule": {"description": "Add an AttestationRule on a WorkloadIdentityPoolManagedIdentity. The total attestation rules after addition must not exceed 50.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}:addAttestationRule", "httpMethod": "POST", "id": "iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.addAttestationRule", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "Required. The resource name of the managed identity or namespace resource to add an attestation rule to.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/namespaces/[^/]+/managedIdentities/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:addAttestationRule", "request": {"$ref": "AddAttestationRuleRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a new WorkloadIdentityPoolManagedIdentity in a WorkloadIdentityPoolNamespace.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities", "httpMethod": "POST", "id": "iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource to create the manage identity in. The only supported location is `global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/namespaces/[^/]+$", "required": true, "type": "string"}, "workloadIdentityPoolManagedIdentityId": {"description": "Required. The ID to use for the managed identity. This value must: * contain at most 63 characters * contain only lowercase alphanumeric characters or `-` * start with an alphanumeric character * end with an alphanumeric character The prefix \"gcp-\" will be reserved for future uses.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/managedIdentities", "request": {"$ref": "WorkloadIdentityPoolManagedIdentity"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a WorkloadIdentityPoolManagedIdentity. You can undelete a managed identity for 30 days. After 30 days, deletion is permanent.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}", "httpMethod": "DELETE", "id": "iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the managed identity to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/namespaces/[^/]+/managedIdentities/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an individual WorkloadIdentityPoolManagedIdentity.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}", "httpMethod": "GET", "id": "iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the managed identity to retrieve.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/namespaces/[^/]+/managedIdentities/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "WorkloadIdentityPoolManagedIdentity"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all non-deleted WorkloadIdentityPoolManagedIdentitys in a namespace. If `show_deleted` is set to `true`, then deleted managed identities are also listed.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities", "httpMethod": "GET", "id": "iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of managed identities to return. If unspecified, at most 50 managed identities are returned. The maximum value is 1000; values above are 1000 truncated to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListWorkloadIdentityPoolManagedIdentities` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource to list managed identities for.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/namespaces/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Whether to return soft-deleted managed identities.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/managedIdentities", "response": {"$ref": "ListWorkloadIdentityPoolManagedIdentitiesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "listAttestationRules": {"description": "List all AttestationRule on a WorkloadIdentityPoolManagedIdentity.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}:listAttestationRules", "httpMethod": "GET", "id": "iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.listAttestationRules", "parameterOrder": ["resource"], "parameters": {"filter": {"description": "Optional. A query filter. Supports the following function: * `container_ids()`: Returns only the AttestationRules under the specific container ids. The function expects a comma-delimited list with only project numbers and must use the format `projects/`. For example: `container_ids(projects/, projects/,...)`.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of AttestationRules to return. If unspecified, at most 50 AttestationRules are returned. The maximum value is 100; values above 100 are truncated to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListWorkloadIdentityPoolProviderKeys` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "resource": {"description": "Required. The resource name of the managed identity or namespace resource to list attestation rules of.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/namespaces/[^/]+/managedIdentities/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:listAttestationRules", "response": {"$ref": "ListAttestationRulesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing WorkloadIdentityPoolManagedIdentity in a WorkloadIdentityPoolNamespace.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}", "httpMethod": "PATCH", "id": "iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name of the managed identity.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/namespaces/[^/]+/managedIdentities/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "WorkloadIdentityPoolManagedIdentity"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "removeAttestationRule": {"description": "Remove an AttestationRule on a WorkloadIdentityPoolManagedIdentity.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}:removeAttestationRule", "httpMethod": "POST", "id": "iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.removeAttestationRule", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "Required. The resource name of the managed identity or namespace resource to remove an attestation rule from.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/namespaces/[^/]+/managedIdentities/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:removeAttestationRule", "request": {"$ref": "RemoveAttestationRuleRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setAttestationRules": {"description": "Set all AttestationRule on a WorkloadIdentityPoolManagedIdentity. A maximum of 50 AttestationRules can be set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}:setAttestationRules", "httpMethod": "POST", "id": "iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.setAttestationRules", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "Required. The resource name of the managed identity or namespace resource to add an attestation rule to.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/namespaces/[^/]+/managedIdentities/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setAttestationRules", "request": {"$ref": "SetAttestationRulesRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "undelete": {"description": "Undeletes a WorkloadIdentityPoolManagedIdentity, as long as it was deleted fewer than 30 days ago.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}:undelete", "httpMethod": "POST", "id": "iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the managed identity to undelete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/namespaces/[^/]+/managedIdentities/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:undelete", "request": {"$ref": "UndeleteWorkloadIdentityPoolManagedIdentityRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}/operations/{operationsId}", "httpMethod": "GET", "id": "iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/namespaces/[^/]+/managedIdentities/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "workloadSources": {"resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}/workloadSources/{workloadSourcesId}/operations/{operationsId}", "httpMethod": "GET", "id": "iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.workloadSources.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/namespaces/[^/]+/managedIdentities/[^/]+/workloadSources/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/operations/{operationsId}", "httpMethod": "GET", "id": "iam.projects.locations.workloadIdentityPools.namespaces.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/namespaces/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/operations/{operationsId}", "httpMethod": "GET", "id": "iam.projects.locations.workloadIdentityPools.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "providers": {"methods": {"create": {"description": "Creates a new WorkloadIdentityPoolProvider in a WorkloadIdentityPool. You cannot reuse the name of a deleted provider until 30 days after deletion.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers", "httpMethod": "POST", "id": "iam.projects.locations.workloadIdentityPools.providers.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The pool to create this provider in.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+$", "required": true, "type": "string"}, "workloadIdentityPoolProviderId": {"description": "Required. The ID for the provider, which becomes the final component of the resource name. This value must be 4-32 characters, and may contain the characters [a-z0-9-]. The prefix `gcp-` is reserved for use by Google, and may not be specified.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/providers", "request": {"$ref": "WorkloadIdentityPoolProvider"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a WorkloadIdentityPoolProvider. Deleting a provider does not revoke credentials that have already been issued; they continue to grant access. You can undelete a provider for 30 days. After 30 days, deletion is permanent. You cannot update deleted providers. However, you can view and list them.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}", "httpMethod": "DELETE", "id": "iam.projects.locations.workloadIdentityPools.providers.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the provider to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/providers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an individual WorkloadIdentityPoolProvider.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}", "httpMethod": "GET", "id": "iam.projects.locations.workloadIdentityPools.providers.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the provider to retrieve.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/providers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "WorkloadIdentityPoolProvider"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all non-deleted WorkloadIdentityPoolProviders in a WorkloadIdentityPool. If `show_deleted` is set to `true`, then deleted providers are also listed.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers", "httpMethod": "GET", "id": "iam.projects.locations.workloadIdentityPools.providers.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of providers to return. If unspecified, at most 50 providers are returned. The maximum value is 100; values above 100 are truncated to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListWorkloadIdentityPoolProviders` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The pool to list providers for.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Whether to return soft-deleted providers.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/providers", "response": {"$ref": "ListWorkloadIdentityPoolProvidersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing WorkloadIdentityPoolProvider.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}", "httpMethod": "PATCH", "id": "iam.projects.locations.workloadIdentityPools.providers.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name of the provider.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/providers/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "WorkloadIdentityPoolProvider"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "undelete": {"description": "Undeletes a WorkloadIdentityPoolProvider, as long as it was deleted fewer than 30 days ago.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}:undelete", "httpMethod": "POST", "id": "iam.projects.locations.workloadIdentityPools.providers.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the provider to undelete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/providers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:undelete", "request": {"$ref": "UndeleteWorkloadIdentityPoolProviderRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"keys": {"methods": {"create": {"description": "Create a new WorkloadIdentityPoolProviderKey in a WorkloadIdentityPoolProvider.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}/keys", "httpMethod": "POST", "id": "iam.projects.locations.workloadIdentityPools.providers.keys.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent provider resource to create the key in.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/providers/[^/]+$", "required": true, "type": "string"}, "workloadIdentityPoolProviderKeyId": {"description": "Required. The ID to use for the key, which becomes the final component of the resource name. This value should be 4-32 characters, and may contain the characters [a-z0-9-].", "location": "query", "type": "string"}}, "path": "v1/{+parent}/keys", "request": {"$ref": "WorkloadIdentityPoolProviderKey"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an WorkloadIdentityPoolProviderKey. You can undelete a key for 30 days. After 30 days, deletion is permanent.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}/keys/{keysId}", "httpMethod": "DELETE", "id": "iam.projects.locations.workloadIdentityPools.providers.keys.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the encryption key to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/providers/[^/]+/keys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an individual WorkloadIdentityPoolProviderKey.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}/keys/{keysId}", "httpMethod": "GET", "id": "iam.projects.locations.workloadIdentityPools.providers.keys.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the key to retrieve.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/providers/[^/]+/keys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "WorkloadIdentityPoolProviderKey"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all non-deleted WorkloadIdentityPoolProviderKeys in a project. If show_deleted is set to `true`, then deleted pools are also listed.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}/keys", "httpMethod": "GET", "id": "iam.projects.locations.workloadIdentityPools.providers.keys.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of keys to return. If unspecified, all keys are returned. The maximum value is 10; values above 10 are truncated to 10.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListWorkloadIdentityPoolProviderKeys` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent provider resource to list encryption keys for.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/providers/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Whether to return soft deleted resources as well.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/keys", "response": {"$ref": "ListWorkloadIdentityPoolProviderKeysResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "undelete": {"description": "Undeletes an WorkloadIdentityPoolProviderKey, as long as it was deleted fewer than 30 days ago.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}/keys/{keysId}:undelete", "httpMethod": "POST", "id": "iam.projects.locations.workloadIdentityPools.providers.keys.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the encryption key to undelete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/providers/[^/]+/keys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:undelete", "request": {"$ref": "UndeleteWorkloadIdentityPoolProviderKeyRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}/keys/{keysId}/operations/{operationsId}", "httpMethod": "GET", "id": "iam.projects.locations.workloadIdentityPools.providers.keys.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/providers/[^/]+/keys/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}/operations/{operationsId}", "httpMethod": "GET", "id": "iam.projects.locations.workloadIdentityPools.providers.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+/providers/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}, "roles": {"methods": {"create": {"description": "Creates a new custom Role.", "flatPath": "v1/projects/{projectsId}/roles", "httpMethod": "POST", "id": "iam.projects.roles.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "The `parent` parameter's value depends on the target resource for the request, namely [projects](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles) or [organizations](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles). Each resource type's `parent` value format is described below: * [projects.roles.create](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles/create): `projects/{PROJECT_ID}`. This method creates project-level [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles). Example request URL: `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles` * [organizations.roles.create](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles/create): `organizations/{ORGANIZATION_ID}`. This method creates organization-level [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles). Example request URL: `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles` Note: Wildcard (*) values are invalid; you must specify a complete project ID or organization ID.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/roles", "request": {"$ref": "CreateRoleRequest"}, "response": {"$ref": "Role"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a custom Role. When you delete a custom role, the following changes occur immediately: * You cannot bind a principal to the custom role in an IAM Policy. * Existing bindings to the custom role are not changed, but they have no effect. * By default, the response from ListRoles does not include the custom role. A deleted custom role still counts toward the [custom role limit](https://cloud.google.com/iam/help/limits) until it is permanently deleted. You have 7 days to undelete the custom role. After 7 days, the following changes occur: * The custom role is permanently deleted and cannot be recovered. * If an IAM policy contains a binding to the custom role, the binding is permanently removed. * The custom role no longer counts toward your custom role limit.", "flatPath": "v1/projects/{projectsId}/roles/{rolesId}", "httpMethod": "DELETE", "id": "iam.projects.roles.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Used to perform a consistent read-modify-write.", "format": "byte", "location": "query", "type": "string"}, "name": {"description": "The `name` parameter's value depends on the target resource for the request, namely [projects](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles) or [organizations](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles). Each resource type's `name` value format is described below: * [projects.roles.delete](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles/delete): `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method deletes only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the project level. Example request URL: `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}` * [organizations.roles.delete](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles/delete): `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method deletes only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the organization level. Example request URL: `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a complete project ID or organization ID.", "location": "path", "pattern": "^projects/[^/]+/roles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Role"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the definition of a Role.", "flatPath": "v1/projects/{projectsId}/roles/{rolesId}", "httpMethod": "GET", "id": "iam.projects.roles.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The `name` parameter's value depends on the target resource for the request, namely [roles](https://cloud.google.com/iam/docs/reference/rest/v1/roles), [projects](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles), or [organizations](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles). Each resource type's `name` value format is described below: * [roles.get](https://cloud.google.com/iam/docs/reference/rest/v1/roles/get): `roles/{ROLE_NAME}`. This method returns results from all [predefined roles](https://cloud.google.com/iam/docs/understanding-roles#predefined_roles) in IAM. Example request URL: `https://iam.googleapis.com/v1/roles/{ROLE_NAME}` * [projects.roles.get](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles/get): `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method returns only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the project level. Example request URL: `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}` * [organizations.roles.get](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles/get): `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method returns only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the organization level. Example request URL: `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a complete project ID or organization ID.", "location": "path", "pattern": "^projects/[^/]+/roles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Role"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists every predefined Role that IAM supports, or every custom role that is defined for an organization or project.", "flatPath": "v1/projects/{projectsId}/roles", "httpMethod": "GET", "id": "iam.projects.roles.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional limit on the number of roles to include in the response. The default is 300, and the maximum is 1,000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional pagination token returned in an earlier ListRolesResponse.", "location": "query", "type": "string"}, "parent": {"description": "The `parent` parameter's value depends on the target resource for the request, namely [roles](https://cloud.google.com/iam/docs/reference/rest/v1/roles), [projects](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles), or [organizations](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles). Each resource type's `parent` value format is described below: * [roles.list](https://cloud.google.com/iam/docs/reference/rest/v1/roles/list): An empty string. This method doesn't require a resource; it simply returns all [predefined roles](https://cloud.google.com/iam/docs/understanding-roles#predefined_roles) in IAM. Example request URL: `https://iam.googleapis.com/v1/roles` * [projects.roles.list](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles/list): `projects/{PROJECT_ID}`. This method lists all project-level [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles). Example request URL: `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles` * [organizations.roles.list](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles/list): `organizations/{ORGANIZATION_ID}`. This method lists all organization-level [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles). Example request URL: `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles` Note: Wildcard (*) values are invalid; you must specify a complete project ID or organization ID.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Include Roles that have been deleted.", "location": "query", "type": "boolean"}, "view": {"description": "Optional view for the returned Role objects. When `FULL` is specified, the `includedPermissions` field is returned, which includes a list of all permissions in the role. The default value is `BASIC`, which does not return the `includedPermissions` field.", "enum": ["BASIC", "FULL"], "enumDescriptions": ["Omits the `included_permissions` field. This is the default value.", "Returns all fields."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/roles", "response": {"$ref": "ListRolesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the definition of a custom Role.", "flatPath": "v1/projects/{projectsId}/roles/{rolesId}", "httpMethod": "PATCH", "id": "iam.projects.roles.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The `name` parameter's value depends on the target resource for the request, namely [projects](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles) or [organizations](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles). Each resource type's `name` value format is described below: * [projects.roles.patch](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles/patch): `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method updates only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the project level. Example request URL: `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}` * [organizations.roles.patch](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles/patch): `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method updates only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the organization level. Example request URL: `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a complete project ID or organization ID.", "location": "path", "pattern": "^projects/[^/]+/roles/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "A mask describing which fields in the Role have changed.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Role"}, "response": {"$ref": "Role"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "undelete": {"description": "Undeletes a custom Role.", "flatPath": "v1/projects/{projectsId}/roles/{rolesId}:undelete", "httpMethod": "POST", "id": "iam.projects.roles.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The `name` parameter's value depends on the target resource for the request, namely [projects](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles) or [organizations](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles). Each resource type's `name` value format is described below: * [projects.roles.undelete](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles/undelete): `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method undeletes only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the project level. Example request URL: `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}` * [organizations.roles.undelete](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles/undelete): `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method undeletes only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the organization level. Example request URL: `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a complete project ID or organization ID.", "location": "path", "pattern": "^projects/[^/]+/roles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:undelete", "request": {"$ref": "UndeleteRoleRequest"}, "response": {"$ref": "Role"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "serviceAccounts": {"methods": {"create": {"description": "Creates a ServiceAccount.", "flatPath": "v1/projects/{projectsId}/serviceAccounts", "httpMethod": "POST", "id": "iam.projects.serviceAccounts.create", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the project associated with the service accounts, such as `projects/my-project-123`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}/serviceAccounts", "request": {"$ref": "CreateServiceAccountRequest"}, "response": {"$ref": "ServiceAccount"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a ServiceAccount. **Warning:** After you delete a service account, you might not be able to undelete it. If you know that you need to re-enable the service account in the future, use DisableServiceAccount instead. If you delete a service account, IAM permanently removes the service account 30 days later. Google Cloud cannot recover the service account after it is permanently removed, even if you file a support request. To help avoid unplanned outages, we recommend that you disable the service account before you delete it. Use DisableServiceAccount to disable the service account, then wait at least 24 hours and watch for unintended consequences. If there are no unintended consequences, you can delete the service account.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}", "httpMethod": "DELETE", "id": "iam.projects.serviceAccounts.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the service account. Use one of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative, you can use the `-` wildcard character instead of the project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}` * `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the `-` wildcard character, because it can cause response messages to contain misleading error codes. For example, if you try to access the service account `projects/-/serviceAccounts/<EMAIL>`, which does not exist, the response contains an HTTP `403 Forbidden` error instead of a `404 Not Found` error.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "disable": {"description": "Disables a ServiceAccount immediately. If an application uses the service account to authenticate, that application can no longer call Google APIs or access Google Cloud resources. Existing access tokens for the service account are rejected, and requests for new access tokens will fail. To re-enable the service account, use EnableServiceAccount. After you re-enable the service account, its existing access tokens will be accepted, and you can request new access tokens. To help avoid unplanned outages, we recommend that you disable the service account before you delete it. Use this method to disable the service account, then wait at least 24 hours and watch for unintended consequences. If there are no unintended consequences, you can delete the service account with DeleteServiceAccount.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:disable", "httpMethod": "POST", "id": "iam.projects.serviceAccounts.disable", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the service account. Use one of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative, you can use the `-` wildcard character instead of the project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}` * `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the `-` wildcard character, because it can cause response messages to contain misleading error codes. For example, if you try to access the service account `projects/-/serviceAccounts/<EMAIL>`, which does not exist, the response contains an HTTP `403 Forbidden` error instead of a `404 Not Found` error.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:disable", "request": {"$ref": "DisableServiceAccountRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "enable": {"description": "Enables a ServiceAccount that was disabled by DisableServiceAccount. If the service account is already enabled, then this method has no effect. If the service account was disabled by other means—for example, if Google disabled the service account because it was compromised—you cannot use this method to enable the service account.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:enable", "httpMethod": "POST", "id": "iam.projects.serviceAccounts.enable", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the service account. Use one of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative, you can use the `-` wildcard character instead of the project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}` * `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the `-` wildcard character, because it can cause response messages to contain misleading error codes. For example, if you try to access the service account `projects/-/serviceAccounts/<EMAIL>`, which does not exist, the response contains an HTTP `403 Forbidden` error instead of a `404 Not Found` error.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:enable", "request": {"$ref": "EnableServiceAccountRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a ServiceAccount.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}", "httpMethod": "GET", "id": "iam.projects.serviceAccounts.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the service account. Use one of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative, you can use the `-` wildcard character instead of the project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}` * `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the `-` wildcard character, because it can cause response messages to contain misleading error codes. For example, if you try to access the service account `projects/-/serviceAccounts/<EMAIL>`, which does not exist, the response contains an HTTP `403 Forbidden` error instead of a `404 Not Found` error.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ServiceAccount"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the IAM policy that is attached to a ServiceAccount. This IAM policy specifies which principals have access to the service account. This method does not tell you whether the service account has been granted any roles on other resources. To check whether a service account has role grants on a resource, use the `getIamPolicy` method for that resource. For example, to view the role grants for a project, call the Resource Manager API's [projects.getIamPolicy](https://cloud.google.com/resource-manager/reference/rest/v1/projects/getIamPolicy) method.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:getIamPolicy", "httpMethod": "POST", "id": "iam.projects.serviceAccounts.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists every ServiceAccount that belongs to a specific project.", "flatPath": "v1/projects/{projectsId}/serviceAccounts", "httpMethod": "GET", "id": "iam.projects.serviceAccounts.list", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the project associated with the service accounts, such as `projects/my-project-123`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Optional limit on the number of service accounts to include in the response. Further accounts can subsequently be obtained by including the ListServiceAccountsResponse.next_page_token in a subsequent request. The default is 20, and the maximum is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional pagination token returned in an earlier ListServiceAccountsResponse.next_page_token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/serviceAccounts", "response": {"$ref": "ListServiceAccountsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "<PERSON>es a ServiceAccount.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}", "httpMethod": "PATCH", "id": "iam.projects.serviceAccounts.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the service account. Use one of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative, you can use the `-` wildcard character instead of the project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}` * `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the `-` wildcard character, because it can cause response messages to contain misleading error codes. For example, if you try to access the service account `projects/-/serviceAccounts/<EMAIL>`, which does not exist, the response contains an HTTP `403 Forbidden` error instead of a `404 Not Found` error.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "PatchServiceAccountRequest"}, "response": {"$ref": "ServiceAccount"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the IAM policy that is attached to a ServiceAccount. Use this method to grant or revoke access to the service account. For example, you could grant a principal the ability to impersonate the service account. This method does not enable the service account to access other resources. To grant roles to a service account on a resource, follow these steps: 1. Call the resource's `getIamPolicy` method to get its current IAM policy. 2. Edit the policy so that it binds the service account to an IAM role for the resource. 3. Call the resource's `setIamPolicy` method to update its IAM policy. For detailed instructions, see [Manage access to project, folders, and organizations](https://cloud.google.com/iam/help/service-accounts/granting-access-to-service-accounts) or [Manage access to other resources](https://cloud.google.com/iam/help/access/manage-other-resources).", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:setIamPolicy", "httpMethod": "POST", "id": "iam.projects.serviceAccounts.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "signBlob": {"deprecated": true, "description": " Signs a blob using the system-managed private key for a ServiceAccount.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:signBlob", "httpMethod": "POST", "id": "iam.projects.serviceAccounts.signBlob", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Deprecated. [Migrate to Service Account Credentials API](https://cloud.google.com/iam/help/credentials/migrate-api). The resource name of the service account. Use one of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative, you can use the `-` wildcard character instead of the project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}` * `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the `-` wildcard character, because it can cause response messages to contain misleading error codes. For example, if you try to access the service account `projects/-/serviceAccounts/<EMAIL>`, which does not exist, the response contains an HTTP `403 Forbidden` error instead of a `404 Not Found` error.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:signBlob", "request": {"$ref": "SignBlobRequest"}, "response": {"$ref": "SignBlobResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "signJwt": {"deprecated": true, "description": " Signs a JSON Web Token (JWT) using the system-managed private key for a ServiceAccount.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:signJwt", "httpMethod": "POST", "id": "iam.projects.serviceAccounts.signJwt", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Deprecated. [Migrate to Service Account Credentials API](https://cloud.google.com/iam/help/credentials/migrate-api). The resource name of the service account. Use one of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative, you can use the `-` wildcard character instead of the project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}` * `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the `-` wildcard character, because it can cause response messages to contain misleading error codes. For example, if you try to access the service account `projects/-/serviceAccounts/<EMAIL>`, which does not exist, the response contains an HTTP `403 Forbidden` error instead of a `404 Not Found` error.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:signJwt", "request": {"$ref": "SignJwtRequest"}, "response": {"$ref": "SignJwtResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Tests whether the caller has the specified permissions on a ServiceAccount.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:testIamPermissions", "httpMethod": "POST", "id": "iam.projects.serviceAccounts.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "undelete": {"description": "Restores a deleted ServiceAccount. **Important:** It is not always possible to restore a deleted service account. Use this method only as a last resort. After you delete a service account, IAM permanently removes the service account 30 days later. There is no way to restore a deleted service account that has been permanently removed.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:undelete", "httpMethod": "POST", "id": "iam.projects.serviceAccounts.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the service account. Use one of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative, you can use the `-` wildcard character instead of the project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}` * `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the `-` wildcard character, because it can cause response messages to contain misleading error codes. For example, if you try to access the service account `projects/-/serviceAccounts/<EMAIL>`, which does not exist, the response contains an HTTP `403 Forbidden` error instead of a `404 Not Found` error.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:undelete", "request": {"$ref": "UndeleteServiceAccountRequest"}, "response": {"$ref": "UndeleteServiceAccountResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "update": {"description": "**Note:** We are in the process of deprecating this method. Use PatchServiceAccount instead. Updates a ServiceAccount. You can update only the `display_name` field.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}", "httpMethod": "PUT", "id": "iam.projects.serviceAccounts.update", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the service account. Use one of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative, you can use the `-` wildcard character instead of the project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}` * `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the `-` wildcard character, because it can cause response messages to contain misleading error codes. For example, if you try to access the service account `projects/-/serviceAccounts/<EMAIL>`, which does not exist, the response contains an HTTP `403 Forbidden` error instead of a `404 Not Found` error.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "ServiceAccount"}, "response": {"$ref": "ServiceAccount"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"keys": {"methods": {"create": {"description": "Creates a ServiceAccount<PERSON>ey.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/keys", "httpMethod": "POST", "id": "iam.projects.serviceAccounts.keys.create", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the service account. Use one of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative, you can use the `-` wildcard character instead of the project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}` * `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the `-` wildcard character, because it can cause response messages to contain misleading error codes. For example, if you try to access the service account `projects/-/serviceAccounts/<EMAIL>`, which does not exist, the response contains an HTTP `403 Forbidden` error instead of a `404 Not Found` error.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}/keys", "request": {"$ref": "CreateServiceAccountKeyRequest"}, "response": {"$ref": "ServiceAccount<PERSON>ey"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a ServiceAccountKey. Deleting a service account key does not revoke short-lived credentials that have been issued based on the service account key.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/keys/{keysId}", "httpMethod": "DELETE", "id": "iam.projects.serviceAccounts.keys.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the service account key. Use one of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}/keys/{KEY_ID}` * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}/keys/{KEY_ID}` As an alternative, you can use the `-` wildcard character instead of the project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}/keys/{KEY_ID}` * `projects/-/serviceAccounts/{UNIQUE_ID}/keys/{KEY_ID}` When possible, avoid using the `-` wildcard character, because it can cause response messages to contain misleading error codes. For example, if you try to access the service account key `projects/-/serviceAccounts/<EMAIL>/keys/fake-key`, which does not exist, the response contains an HTTP `403 Forbidden` error instead of a `404 Not Found` error.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+/keys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "disable": {"description": "Disable a ServiceAccountKey. A disabled service account key can be re-enabled with EnableServiceAccountKey.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/keys/{keysId}:disable", "httpMethod": "POST", "id": "iam.projects.serviceAccounts.keys.disable", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the service account key. Use one of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}/keys/{KEY_ID}` * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}/keys/{KEY_ID}` As an alternative, you can use the `-` wildcard character instead of the project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}/keys/{KEY_ID}` * `projects/-/serviceAccounts/{UNIQUE_ID}/keys/{KEY_ID}` When possible, avoid using the `-` wildcard character, because it can cause response messages to contain misleading error codes. For example, if you try to access the service account key `projects/-/serviceAccounts/<EMAIL>/keys/fake-key`, which does not exist, the response contains an HTTP `403 Forbidden` error instead of a `404 Not Found` error.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+/keys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:disable", "request": {"$ref": "DisableServiceAccountKeyRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "enable": {"description": "Enable a ServiceAccount<PERSON>ey.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/keys/{keysId}:enable", "httpMethod": "POST", "id": "iam.projects.serviceAccounts.keys.enable", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the service account key. Use one of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}/keys/{KEY_ID}` * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}/keys/{KEY_ID}` As an alternative, you can use the `-` wildcard character instead of the project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}/keys/{KEY_ID}` * `projects/-/serviceAccounts/{UNIQUE_ID}/keys/{KEY_ID}` When possible, avoid using the `-` wildcard character, because it can cause response messages to contain misleading error codes. For example, if you try to access the service account key `projects/-/serviceAccounts/<EMAIL>/keys/fake-key`, which does not exist, the response contains an HTTP `403 Forbidden` error instead of a `404 Not Found` error.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+/keys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:enable", "request": {"$ref": "EnableServiceAccountKeyRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a ServiceAccount<PERSON>ey.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/keys/{keysId}", "httpMethod": "GET", "id": "iam.projects.serviceAccounts.keys.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the service account key. Use one of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}/keys/{KEY_ID}` * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}/keys/{KEY_ID}` As an alternative, you can use the `-` wildcard character instead of the project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}/keys/{KEY_ID}` * `projects/-/serviceAccounts/{UNIQUE_ID}/keys/{KEY_ID}` When possible, avoid using the `-` wildcard character, because it can cause response messages to contain misleading error codes. For example, if you try to access the service account key `projects/-/serviceAccounts/<EMAIL>/keys/fake-key`, which does not exist, the response contains an HTTP `403 Forbidden` error instead of a `404 Not Found` error.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+/keys/[^/]+$", "required": true, "type": "string"}, "publicKeyType": {"description": "Optional. The output format of the public key. The default is `TYPE_NONE`, which means that the public key is not returned.", "enum": ["TYPE_NONE", "TYPE_X509_PEM_FILE", "TYPE_RAW_PUBLIC_KEY"], "enumDescriptions": ["Do not return the public key.", "X509 PEM format.", "Raw public key."], "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ServiceAccount<PERSON>ey"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists every ServiceAccountKey for a service account.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/keys", "httpMethod": "GET", "id": "iam.projects.serviceAccounts.keys.list", "parameterOrder": ["name"], "parameters": {"keyTypes": {"description": "Filters the types of keys the user wants to include in the list response. Duplicate key types are not allowed. If no key type is provided, all keys are returned.", "enum": ["KEY_TYPE_UNSPECIFIED", "USER_MANAGED", "SYSTEM_MANAGED"], "enumDescriptions": ["Unspecified key type. The presence of this in the message will immediately result in an error.", "User-managed keys (managed and rotated by the user).", "System-managed keys (managed and rotated by Google)."], "location": "query", "repeated": true, "type": "string"}, "name": {"description": "Required. The resource name of the service account. Use one of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative, you can use the `-` wildcard character instead of the project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}` * `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the `-` wildcard character, because it can cause response messages to contain misleading error codes. For example, if you try to access the service account `projects/-/serviceAccounts/<EMAIL>`, which does not exist, the response contains an HTTP `403 Forbidden` error instead of a `404 Not Found` error.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}/keys", "response": {"$ref": "ListServiceAccountKeysResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "upload": {"description": "Uploads the public key portion of a key pair that you manage, and associates the public key with a ServiceAccount. After you upload the public key, you can use the private key from the key pair as a service account key.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/keys:upload", "httpMethod": "POST", "id": "iam.projects.serviceAccounts.keys.upload", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the service account key. Use one of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative, you can use the `-` wildcard character instead of the project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}` * `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the `-` wildcard character, because it can cause response messages to contain misleading error codes. For example, if you try to access the service account `projects/-/serviceAccounts/<EMAIL>`, which does not exist, the response contains an HTTP `403 Forbidden` error instead of a `404 Not Found` error.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}/keys:upload", "request": {"$ref": "UploadServiceAccountKeyRequest"}, "response": {"$ref": "ServiceAccount<PERSON>ey"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "roles": {"methods": {"get": {"description": "Gets the definition of a Role.", "flatPath": "v1/roles/{rolesId}", "httpMethod": "GET", "id": "iam.roles.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The `name` parameter's value depends on the target resource for the request, namely [roles](https://cloud.google.com/iam/docs/reference/rest/v1/roles), [projects](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles), or [organizations](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles). Each resource type's `name` value format is described below: * [roles.get](https://cloud.google.com/iam/docs/reference/rest/v1/roles/get): `roles/{ROLE_NAME}`. This method returns results from all [predefined roles](https://cloud.google.com/iam/docs/understanding-roles#predefined_roles) in IAM. Example request URL: `https://iam.googleapis.com/v1/roles/{ROLE_NAME}` * [projects.roles.get](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles/get): `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method returns only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the project level. Example request URL: `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}` * [organizations.roles.get](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles/get): `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method returns only [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that have been created at the organization level. Example request URL: `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a complete project ID or organization ID.", "location": "path", "pattern": "^roles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Role"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists every predefined Role that IAM supports, or every custom role that is defined for an organization or project.", "flatPath": "v1/roles", "httpMethod": "GET", "id": "iam.roles.list", "parameterOrder": [], "parameters": {"pageSize": {"description": "Optional limit on the number of roles to include in the response. The default is 300, and the maximum is 1,000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional pagination token returned in an earlier ListRolesResponse.", "location": "query", "type": "string"}, "parent": {"description": "The `parent` parameter's value depends on the target resource for the request, namely [roles](https://cloud.google.com/iam/docs/reference/rest/v1/roles), [projects](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles), or [organizations](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles). Each resource type's `parent` value format is described below: * [roles.list](https://cloud.google.com/iam/docs/reference/rest/v1/roles/list): An empty string. This method doesn't require a resource; it simply returns all [predefined roles](https://cloud.google.com/iam/docs/understanding-roles#predefined_roles) in IAM. Example request URL: `https://iam.googleapis.com/v1/roles` * [projects.roles.list](https://cloud.google.com/iam/docs/reference/rest/v1/projects.roles/list): `projects/{PROJECT_ID}`. This method lists all project-level [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles). Example request URL: `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles` * [organizations.roles.list](https://cloud.google.com/iam/docs/reference/rest/v1/organizations.roles/list): `organizations/{ORGANIZATION_ID}`. This method lists all organization-level [custom roles](https://cloud.google.com/iam/docs/understanding-custom-roles). Example request URL: `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles` Note: Wildcard (*) values are invalid; you must specify a complete project ID or organization ID.", "location": "query", "type": "string"}, "showDeleted": {"description": "Include Roles that have been deleted.", "location": "query", "type": "boolean"}, "view": {"description": "Optional view for the returned Role objects. When `FULL` is specified, the `includedPermissions` field is returned, which includes a list of all permissions in the role. The default value is `BASIC`, which does not return the `includedPermissions` field.", "enum": ["BASIC", "FULL"], "enumDescriptions": ["Omits the `included_permissions` field. This is the default value.", "Returns all fields."], "location": "query", "type": "string"}}, "path": "v1/roles", "response": {"$ref": "ListRolesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "queryGrantableRoles": {"description": "Lists roles that can be granted on a Google Cloud resource. A role is grantable if the IAM policy for the resource can contain bindings to the role.", "flatPath": "v1/roles:queryGrantableRoles", "httpMethod": "POST", "id": "iam.roles.queryGrantableRoles", "parameterOrder": [], "parameters": {}, "path": "v1/roles:queryGrantableRoles", "request": {"$ref": "QueryGrantableRolesRequest"}, "response": {"$ref": "QueryGrantableRolesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}, "revision": "20250516", "rootUrl": "https://iam.googleapis.com/", "schemas": {"AccessRestrictions": {"description": "Access related restrictions on the workforce pool.", "id": "AccessRestrictions", "properties": {"allowedServices": {"description": "Optional. Immutable. Services allowed for web sign-in with the workforce pool. If not set by default there are no restrictions.", "items": {"$ref": "ServiceConfig"}, "type": "array"}, "disableProgrammaticSignin": {"description": "Optional. Disable programmatic sign-in by disabling token issue via the Security Token API endpoint. See [Security Token Service API] (https://cloud.google.com/iam/docs/reference/sts/rest).", "type": "boolean"}}, "type": "object"}, "AddAttestationRuleRequest": {"description": "Request message for AddAttestationRule.", "id": "AddAttestationRuleRequest", "properties": {"attestationRule": {"$ref": "AttestationRule", "description": "Required. The attestation rule to be added."}}, "type": "object"}, "AdminAuditData": {"description": "Audit log information specific to Cloud IAM admin APIs. This message is serialized as an `Any` type in the `ServiceData` message of an `AuditLog` message.", "id": "AdminAuditData", "properties": {"permissionDelta": {"$ref": "PermissionDelta", "description": "The permission_delta when when creating or updating a Role."}}, "type": "object"}, "AttestationRule": {"description": "Defines which workloads can receive an identity within a pool. When an AttestationRule is defined under a managed identity, matching workloads may receive that identity.", "id": "AttestationRule", "properties": {"googleCloudResource": {"description": "Optional. A single workload operating on Google Cloud. For example: `//compute.googleapis.com/projects/123/uid/zones/us-central1-a/instances/12345`.", "type": "string"}}, "type": "object"}, "AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditData": {"description": "Audit log information specific to Cloud IAM. This message is serialized as an `Any` type in the `ServiceData` message of an `AuditLog` message.", "id": "AuditData", "properties": {"policyDelta": {"$ref": "PolicyDelta", "description": "Policy delta between the original policy and the newly set policy."}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "AuditableService": {"description": "Contains information about an auditable service.", "id": "AuditableService", "properties": {"name": {"description": "Public name of the service. For example, the service name for IAM is 'iam.googleapis.com'.", "type": "string"}}, "type": "object"}, "Aws": {"description": "Represents an Amazon Web Services identity provider.", "id": "Aws", "properties": {"accountId": {"description": "Required. The AWS account ID.", "type": "string"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "BindingDelta": {"description": "One delta entry for Binding. Each individual change (only one member in each entry) to a binding will be a separate entry.", "id": "BindingDelta", "properties": {"action": {"description": "The action that was performed on a Binding. Required", "enum": ["ACTION_UNSPECIFIED", "ADD", "REMOVE"], "enumDescriptions": ["Unspecified.", "Addition of a Binding.", "Removal of a Binding."], "type": "string"}, "condition": {"$ref": "Expr", "description": "The condition that is associated with this binding."}, "member": {"description": "A single identity requesting access for a Google Cloud resource. Follows the same format of Binding.members. Required", "type": "string"}, "role": {"description": "Role that is assigned to `members`. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. Required", "type": "string"}}, "type": "object"}, "CreateRoleRequest": {"description": "The request to create a new role.", "id": "CreateRoleRequest", "properties": {"role": {"$ref": "Role", "description": "The Role resource to create."}, "roleId": {"description": "The role ID to use for this role. A role ID may contain alphanumeric characters, underscores (`_`), and periods (`.`). It must contain a minimum of 3 characters and a maximum of 64 characters.", "type": "string"}}, "type": "object"}, "CreateServiceAccountKeyRequest": {"description": "The service account key create request.", "id": "CreateServiceAccountKeyRequest", "properties": {"keyAlgorithm": {"description": "Which type of key and algorithm to use for the key. The default is currently a 2K RSA key. However this may change in the future.", "enum": ["KEY_ALG_UNSPECIFIED", "KEY_ALG_RSA_1024", "KEY_ALG_RSA_2048"], "enumDescriptions": ["An unspecified key algorithm.", "1k RSA Key.", "2k RSA Key."], "type": "string"}, "privateKeyType": {"description": "The output format of the private key. The default value is `TYPE_GOOGLE_CREDENTIALS_FILE`, which is the Google Credentials File format.", "enum": ["TYPE_UNSPECIFIED", "TYPE_PKCS12_FILE", "TYPE_GOOGLE_CREDENTIALS_FILE"], "enumDescriptions": ["Unspecified. Equivalent to `TYPE_GOOGLE_CREDENTIALS_FILE`.", "PKCS12 format. The password for the PKCS12 file is `notasecret`. For more information, see https://tools.ietf.org/html/rfc7292.", "Google Credentials File format."], "type": "string"}}, "type": "object"}, "CreateServiceAccountRequest": {"description": "The service account create request.", "id": "CreateServiceAccountRequest", "properties": {"accountId": {"description": "Required. The account id that is used to generate the service account email address and a stable unique id. It is unique within a project, must be 6-30 characters long, and match the regular expression `[a-z]([-a-z0-9]*[a-z0-9])` to comply with RFC1035.", "type": "string"}, "serviceAccount": {"$ref": "ServiceAccount", "description": "The ServiceAccount resource to create. Currently, only the following values are user assignable: `display_name` and `description`."}}, "type": "object"}, "DisableServiceAccountKeyRequest": {"description": "The service account key disable request.", "id": "DisableServiceAccountKeyRequest", "properties": {"extendedStatusMessage": {"description": "Optional. Usable by internal google services only. An extended_status_message can be used to include additional information about the key, such as its private key data being exposed on a public repository like GitHub.", "type": "string"}, "serviceAccountKeyDisableReason": {"description": "Optional. Describes the reason this key is being disabled. If unspecified, the default value of SERVICE_ACCOUNT_KEY_DISABLE_REASON_USER_INITIATED will be used.", "enum": ["SERVICE_ACCOUNT_KEY_DISABLE_REASON_UNSPECIFIED", "SERVICE_ACCOUNT_KEY_DISABLE_REASON_USER_INITIATED", "SERVICE_ACCOUNT_KEY_DISABLE_REASON_EXPOSED", "SERVICE_ACCOUNT_KEY_DISABLE_REASON_COMPROMISE_DETECTED"], "enumDescriptions": ["Unspecified disable reason", "Disabled by the user", "Google detected this Service Account external key's private key data as exposed, typically in a public repository on GitHub or similar.", "This service account external key was detected as compromised and used by an attacker."], "type": "string"}}, "type": "object"}, "DisableServiceAccountRequest": {"description": "The service account disable request.", "id": "DisableServiceAccountRequest", "properties": {}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "EnableServiceAccountKeyRequest": {"description": "The service account key enable request.", "id": "EnableServiceAccountKeyRequest", "properties": {}, "type": "object"}, "EnableServiceAccountRequest": {"description": "The service account enable request.", "id": "EnableServiceAccountRequest", "properties": {}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "ExtendedStatus": {"description": "Extended status can store additional metadata. For example, for keys disabled due to their private key data being expoesed we may include a message with more information about the exposure.", "id": "ExtendedStatus", "properties": {"key": {"description": "The key for this extended status.", "enum": ["SERVICE_ACCOUNT_KEY_EXTENDED_STATUS_KEY_UNSPECIFIED", "SERVICE_ACCOUNT_KEY_EXTENDED_STATUS_KEY_EXPOSED", "SERVICE_ACCOUNT_KEY_EXTENDED_STATUS_KEY_COMPROMISE_DETECTED"], "enumDescriptions": ["Unspecified extended status, should not be used.", "This key has been detected as exposed. extended_status_value may contain information about the exposure (public GitHub repo, open internet, etc.)", "This key was implicated in a compromise or other attack. extended_status_value may contain information about the abuse perpetrated."], "type": "string"}, "value": {"description": "The value for the extended status.", "type": "string"}}, "type": "object"}, "GetIamPolicyRequest": {"description": "Request message for `GetIamPolicy` method.", "id": "GetIamPolicyRequest", "properties": {"options": {"$ref": "GetPolicyOptions", "description": "OPTIONAL: A `GetPolicyOptions` object for specifying options to `GetIamPolicy`."}}, "type": "object"}, "GetPolicyOptions": {"description": "Encapsulates settings provided to GetIamPolicy.", "id": "GetPolicyOptions", "properties": {"requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleIamAdminV1WorkforcePoolProviderExtraAttributesOAuth2Client": {"description": "Represents the OAuth 2.0 client credential configuration for retrieving additional user attributes that are not present in the initial authentication credentials from the identity provider, e.g. groups. See https://datatracker.ietf.org/doc/html/rfc6749#section-4.4 for more details on client credentials grant flow.", "id": "GoogleIamAdminV1WorkforcePoolProviderExtraAttributesOAuth2Client", "properties": {"attributesType": {"description": "Required. Represents the IdP and type of claims that should be fetched.", "enum": ["ATTRIBUTES_TYPE_UNSPECIFIED", "AZURE_AD_GROUPS_MAIL", "AZURE_AD_GROUPS_ID"], "enumDescriptions": ["No AttributesType specified.", "Used to get the user's group claims from the Microsoft Entra ID identity provider using the configuration provided in ExtraAttributesOAuth2Client. The `mail` property of the `microsoft.graph.group` object is used for claim mapping. See https://learn.microsoft.com/en-us/graph/api/resources/group?view=graph-rest-1.0#properties for more details on `microsoft.graph.group` properties. The group mail addresses of the user's groups that are returned from Microsoft Entra ID can be mapped by using the following attributes: * OIDC: `assertion.groups` * SAML: `assertion.attributes.groups`", "Used to get the user's group claims from the Microsoft Entra ID identity provider using the configuration provided in ExtraAttributesOAuth2Client. The `id` property of the `microsoft.graph.group` object is used for claim mapping. See https://learn.microsoft.com/en-us/graph/api/resources/group?view=graph-rest-1.0#properties for more details on `microsoft.graph.group` properties. The group IDs of the user's groups that are returned from Microsoft Entra ID can be mapped by using the following attributes: * OIDC: `assertion.groups` * SAML: `assertion.attributes.groups`"], "type": "string"}, "clientId": {"description": "Required. The OAuth 2.0 client ID for retrieving extra attributes from the identity provider. Required to get the Access Token using client credentials grant flow.", "type": "string"}, "clientSecret": {"$ref": "GoogleIamAdminV1WorkforcePoolProviderOidcClientSecret", "description": "Required. The OAuth 2.0 client secret for retrieving extra attributes from the identity provider. Required to get the Access Token using client credentials grant flow."}, "issuerUri": {"description": "Required. The OIDC identity provider's issuer URI. Must be a valid URI using the `https` scheme. Required to get the OIDC discovery document.", "type": "string"}, "queryParameters": {"$ref": "GoogleIamAdminV1WorkforcePoolProviderExtraAttributesOAuth2ClientQueryParameters", "description": "Optional. Represents the parameters to control which claims are fetched from an IdP."}}, "type": "object"}, "GoogleIamAdminV1WorkforcePoolProviderExtraAttributesOAuth2ClientQueryParameters": {"description": "Represents the parameters to control which claims are fetched from an IdP.", "id": "GoogleIamAdminV1WorkforcePoolProviderExtraAttributesOAuth2ClientQueryParameters", "properties": {"filter": {"description": "Optional. The filter used to request specific records from the IdP. By default, all of the groups that are associated with a user are fetched. For Microsoft Entra ID, you can add `$search` query parameters using [Keyword Query Language] (https://learn.microsoft.com/en-us/sharepoint/dev/general-development/keyword-query-language-kql-syntax-reference). To learn more about `$search` querying in Microsoft Entra ID, see [Use the `$search` query parameter] (https://learn.microsoft.com/en-us/graph/search-query-parameter). Additionally, Workforce Identity Federation automatically adds the following [`$filter` query parameters] (https://learn.microsoft.com/en-us/graph/filter-query-parameter), based on the value of `attributes_type`. Values passed to `filter` are converted to `$search` query parameters. Additional `$filter` query parameters cannot be added using this field. * `AZURE_AD_GROUPS_MAIL`: `mailEnabled` and `securityEnabled` filters are applied. * `AZURE_AD_GROUPS_ID`: `securityEnabled` filter is applied.", "type": "string"}}, "type": "object"}, "GoogleIamAdminV1WorkforcePoolProviderOidc": {"description": "Represents an OpenId Connect 1.0 identity provider.", "id": "GoogleIamAdminV1WorkforcePoolProviderOidc", "properties": {"clientId": {"description": "Required. The client ID. Must match the audience claim of the JWT issued by the identity provider.", "type": "string"}, "clientSecret": {"$ref": "GoogleIamAdminV1WorkforcePoolProviderOidcClientSecret", "description": "Optional. The optional client secret. Required to enable Authorization Code flow for web sign-in."}, "issuerUri": {"description": "Required. The OIDC issuer URI. Must be a valid URI using the `https` scheme.", "type": "string"}, "jwksJson": {"description": "Optional. OIDC JWKs in JSON String format. For details on the definition of a JWK, see https://tools.ietf.org/html/rfc7517. If not set, the `jwks_uri` from the discovery document(fetched from the .well-known path of the `issuer_uri`) will be used. Currently, RSA and EC asymmetric keys are supported. The JWK must use following format and include only the following fields: { \"keys\": [ { \"kty\": \"RSA/EC\", \"alg\": \"\", \"use\": \"sig\", \"kid\": \"\", \"n\": \"\", \"e\": \"\", \"x\": \"\", \"y\": \"\", \"crv\": \"\" } ] }", "type": "string"}, "webSsoConfig": {"$ref": "GoogleIamAdminV1WorkforcePoolProviderOidcWebSsoConfig", "description": "Required. Configuration for web single sign-on for the OIDC provider. Here, web sign-in refers to console sign-in and gcloud sign-in through the browser."}}, "type": "object"}, "GoogleIamAdminV1WorkforcePoolProviderOidcClientSecret": {"description": "Representation of a client secret configured for the OIDC provider.", "id": "GoogleIamAdminV1WorkforcePoolProviderOidcClientSecret", "properties": {"value": {"$ref": "GoogleIamAdminV1WorkforcePoolProviderOidcClientSecretValue", "description": "The value of the client secret."}}, "type": "object"}, "GoogleIamAdminV1WorkforcePoolProviderOidcClientSecretValue": {"description": "Representation of the value of the client secret.", "id": "GoogleIamAdminV1WorkforcePoolProviderOidcClientSecretValue", "properties": {"plainText": {"description": "Optional. Input only. The plain text of the client secret value. For security reasons, this field is only used for input and will never be populated in any response.", "type": "string"}, "thumbprint": {"description": "Output only. A thumbprint to represent the current client secret value.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleIamAdminV1WorkforcePoolProviderOidcWebSsoConfig": {"description": "Configuration for web single sign-on for the OIDC provider.", "id": "GoogleIamAdminV1WorkforcePoolProviderOidcWebSsoConfig", "properties": {"additionalScopes": {"description": "Optional. Additional scopes to request for in the OIDC authentication request on top of scopes requested by default. By default, the `openid`, `profile` and `email` scopes that are supported by the identity provider are requested. Each additional scope may be at most 256 characters. A maximum of 10 additional scopes may be configured.", "items": {"type": "string"}, "type": "array"}, "assertionClaimsBehavior": {"description": "Required. The behavior for how OIDC Claims are included in the `assertion` object used for attribute mapping and attribute condition.", "enum": ["ASSERTION_CLAIMS_BEHAVIOR_UNSPECIFIED", "MERGE_USER_INFO_OVER_ID_TOKEN_CLAIMS", "ONLY_ID_TOKEN_CLAIMS"], "enumDescriptions": ["No assertion claims behavior specified.", "Merge the UserInfo Endpoint Claims with ID Token Claims, preferring UserInfo Claim Values for the same Claim Name. This option is available only for the Authorization Code Flow.", "Only include ID Token Claims."], "type": "string"}, "responseType": {"description": "Required. The Response Type to request for in the OIDC Authorization Request for web sign-in. The `CODE` Response Type is recommended to avoid the Implicit Flow, for security reasons.", "enum": ["RESPONSE_TYPE_UNSPECIFIED", "CODE", "ID_TOKEN"], "enumDescriptions": ["No Response Type specified.", "The `response_type=code` selection uses the Authorization Code Flow for web sign-in. Requires a configured client secret.", "The `response_type=id_token` selection uses the Implicit Flow for web sign-in."], "type": "string"}}, "type": "object"}, "GoogleIamAdminV1WorkforcePoolProviderSaml": {"description": "Represents a SAML identity provider.", "id": "GoogleIamAdminV1WorkforcePoolProviderSaml", "properties": {"idpMetadataXml": {"description": "Required. SAML Identity provider configuration metadata xml doc. The xml document should comply with [SAML 2.0 specification](https://docs.oasis-open.org/security/saml/v2.0/saml-metadata-2.0-os.pdf). The max size of the acceptable xml document will be bounded to 128k characters. The metadata xml document should satisfy the following constraints: 1) Must contain an Identity Provider Entity ID. 2) Must contain at least one non-expired signing key certificate. 3) For each signing key: a) Valid from should be no more than 7 days from now. b) Valid to should be no more than 20 years in the future. 4) Up to 3 IdP signing keys are allowed in the metadata xml. When updating the provider's metadata xml, at least one non-expired signing key must overlap with the existing metadata. This requirement is skipped if there are no non-expired signing keys present in the existing metadata.", "type": "string"}}, "type": "object"}, "InlineCertificateIssuanceConfig": {"description": "Represents configuration for generating mutual TLS (mTLS) certificates for the identities within this pool.", "id": "InlineCertificateIssuanceConfig", "properties": {"caPools": {"additionalProperties": {"type": "string"}, "description": "Optional. A required mapping of a Google Cloud region to the CA pool resource located in that region. The CA pool is used for certificate issuance, adhering to the following constraints: * Key format: A supported cloud region name equivalent to the location identifier in the corresponding map entry's value. * Value format: A valid CA pool resource path format like: \"projects/{project}/locations/{location}/caPools/{ca_pool}\" * Region Matching: Workloads are ONLY issued certificates from CA pools within the same region. Also the CA pool region (in value) must match the workload's region (key).", "type": "object"}, "keyAlgorithm": {"description": "Optional. Key algorithm to use when generating the key pair. This key pair will be used to create the certificate. If not specified, this will default to ECDSA_P256.", "enum": ["KEY_ALGORITHM_UNSPECIFIED", "RSA_2048", "RSA_3072", "RSA_4096", "ECDSA_P256", "ECDSA_P384"], "enumDescriptions": ["Unspecified key algorithm. Defaults to ECDSA_P256.", "Specifies RSA with a 2048-bit modulus.", "Specifies RSA with a 3072-bit modulus.", "Specifies RSA with a 4096-bit modulus.", "Specifies ECDSA with curve P256.", "Specifies ECDSA with curve P384."], "type": "string"}, "lifetime": {"description": "Optional. Lifetime of the workload certificates issued by the CA pool. Must be between 24 hours and 30 days. If not specified, this will be defaulted to 24 hours.", "format": "google-duration", "type": "string"}, "rotationWindowPercentage": {"description": "Optional. Rotation window percentage, the percentage of remaining lifetime after which certificate rotation is initiated. Must be between 50 and 80. If no value is specified, rotation window percentage is defaulted to 50.", "format": "int32", "type": "integer"}}, "type": "object"}, "InlineTrustConfig": {"description": "Defines configuration for extending trust to additional trust domains. By establishing trust with another domain, the current domain will recognize and accept certificates issued by entities within the trusted domains. Note that a trust domain automatically trusts itself, eliminating the need for explicit configuration.", "id": "InlineTrustConfig", "properties": {"additionalTrustBundles": {"additionalProperties": {"$ref": "TrustStore"}, "description": "Optional. Maps specific trust domains (e.g., \"example.com\") to their corresponding TrustStore, which contain the trusted root certificates for that domain. There can be a maximum of 10 trust domain entries in this map. Note that a trust domain automatically trusts itself and don't need to be specified here. If however, this WorkloadIdentityPool's trust domain contains any trust anchors in the additional_trust_bundles map, those trust anchors will be *appended to* the trust bundle automatically derived from your InlineCertificateIssuanceConfig's ca_pools.", "type": "object"}}, "type": "object"}, "IntermediateCA": {"description": "Intermediate CA certificates used for building the trust chain to trust anchor", "id": "IntermediateCA", "properties": {"pemCertificate": {"description": "PEM certificate of the PKI used for validation. Must only contain one ca certificate.", "type": "string"}}, "type": "object"}, "KeyData": {"description": "Represents a public key data along with its format.", "id": "KeyData", "properties": {"format": {"description": "Output only. The format of the key.", "enum": ["KEY_FORMAT_UNSPECIFIED", "RSA_X509_PEM"], "enumDescriptions": ["No format has been specified. This is an invalid format and must not be used.", "A RSA public key wrapped in an X.509v3 certificate ([RFC5280] ( https://www.ietf.org/rfc/rfc5280.txt)), encoded in base64, and wrapped in [public certificate label](https://datatracker.ietf.org/doc/html/rfc7468#section-5.1)."], "readOnly": true, "type": "string"}, "key": {"description": "Output only. The key data. The format of the key is represented by the format field.", "readOnly": true, "type": "string"}, "keySpec": {"description": "Required. The specifications for the key.", "enum": ["KEY_SPEC_UNSPECIFIED", "RSA_2048", "RSA_3072", "RSA_4096"], "enumDescriptions": ["No key specification specified.", "A 2048 bit RSA key.", "A 3072 bit RSA key.", "A 4096 bit RSA key."], "type": "string"}, "notAfterTime": {"description": "Output only. Latest timestamp when this key is valid. Attempts to use this key after this time will fail. Only present if the key data represents a X.509 certificate.", "format": "google-datetime", "readOnly": true, "type": "string"}, "notBeforeTime": {"description": "Output only. Earliest timestamp when this key is valid. Attempts to use this key before this time will fail. Only present if the key data represents a X.509 certificate.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "LintPolicyRequest": {"description": "The request to lint an IAM policy object.", "id": "LintPolicyRequest", "properties": {"condition": {"$ref": "Expr", "description": "google.iam.v1.Binding.condition object to be linted."}, "fullResourceName": {"description": "The full resource name of the policy this lint request is about. The name follows the Google Cloud format for full resource names. For example, a Google Cloud project with ID `my-project` will be named `//cloudresourcemanager.googleapis.com/projects/my-project`. The resource name is not used to read a policy from IAM. Only the data in the request object is linted.", "type": "string"}}, "type": "object"}, "LintPolicyResponse": {"description": "The response of a lint operation. An empty response indicates the operation was able to fully execute and no lint issue was found.", "id": "LintPolicyResponse", "properties": {"lintResults": {"description": "List of lint results sorted by `severity` in descending order.", "items": {"$ref": "LintResult"}, "type": "array"}}, "type": "object"}, "LintResult": {"description": "Structured response of a single validation unit.", "id": "LintResult", "properties": {"debugMessage": {"description": "Human readable debug message associated with the issue.", "type": "string"}, "fieldName": {"description": "The name of the field for which this lint result is about. For nested messages `field_name` consists of names of the embedded fields separated by period character. The top-level qualifier is the input object to lint in the request. For example, the `field_name` value `condition.expression` identifies a lint result for the `expression` field of the provided condition.", "type": "string"}, "level": {"description": "The validation unit level.", "enum": ["LEVEL_UNSPECIFIED", "CONDITION"], "enumDescriptions": ["Level is unspecified.", "A validation unit which operates on an individual condition within a binding."], "type": "string"}, "locationOffset": {"description": "0-based character position of problematic construct within the object identified by `field_name`. Currently, this is populated only for condition expression.", "format": "int32", "type": "integer"}, "severity": {"description": "The validation unit severity.", "enum": ["SEVERITY_UNSPECIFIED", "ERROR", "WARNING", "NOTICE", "INFO", "DEPRECATED"], "enumDescriptions": ["Severity is unspecified.", "A validation unit returns an error only for critical issues. If an attempt is made to set the problematic policy without rectifying the critical issue, it causes the `setPolicy` operation to fail.", "Any issue which is severe enough but does not cause an error. For example, suspicious constructs in the input object will not necessarily fail `setPolicy`, but there is a high likelihood that they won't behave as expected during policy evaluation in `checkPolicy`. This includes the following common scenarios: - Unsatisfiable condition: Expired timestamp in date/time condition. - Ineffective condition: Condition on a pair which is granted unconditionally in another binding of the same policy.", "Reserved for the issues that are not severe as `ERROR`/`WARNING`, but need special handling. For instance, messages about skipped validation units are issued as `NOTICE`.", "Any informative statement which is not severe enough to raise `ERROR`/`WARNING`/`NOTICE`, like auto-correction recommendations on the input content. Note that current version of the linter does not utilize `INFO`.", "Deprecated severity level."], "type": "string"}, "validationUnitName": {"description": "The validation unit name, for instance \"lintValidationUnits/ConditionComplexityCheck\".", "type": "string"}}, "type": "object"}, "ListAttestationRulesResponse": {"description": "Response message for ListAttestationRules.", "id": "ListAttestationRulesResponse", "properties": {"attestationRules": {"description": "A list of AttestationRules.", "items": {"$ref": "AttestationRule"}, "type": "array"}, "nextPageToken": {"description": "Optional. A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListOauthClientCredentialsResponse": {"description": "Response message for ListOauthClientCredentials.", "id": "ListOauthClientCredentialsResponse", "properties": {"oauthClientCredentials": {"description": "A list of OauthClientCredentials.", "items": {"$ref": "OauthClientCredential"}, "type": "array"}}, "type": "object"}, "ListOauthClientsResponse": {"description": "Response message for ListOauthClients.", "id": "ListOauthClientsResponse", "properties": {"nextPageToken": {"description": "Optional. A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "oauthClients": {"description": "A list of OauthClients.", "items": {"$ref": "OauthClient"}, "type": "array"}}, "type": "object"}, "ListRolesResponse": {"description": "The response containing the roles defined under a resource.", "id": "ListRolesResponse", "properties": {"nextPageToken": {"description": "To retrieve the next page of results, set `ListRolesRequest.page_token` to this value.", "type": "string"}, "roles": {"description": "The Roles defined on this resource.", "items": {"$ref": "Role"}, "type": "array"}}, "type": "object"}, "ListServiceAccountKeysResponse": {"description": "The service account keys list response.", "id": "ListServiceAccountKeysResponse", "properties": {"keys": {"description": "The public keys for the service account.", "items": {"$ref": "ServiceAccount<PERSON>ey"}, "type": "array"}}, "type": "object"}, "ListServiceAccountsResponse": {"description": "The service account list response.", "id": "ListServiceAccountsResponse", "properties": {"accounts": {"description": "The list of matching service accounts.", "items": {"$ref": "ServiceAccount"}, "type": "array"}, "nextPageToken": {"description": "To retrieve the next page of results, set ListServiceAccountsRequest.page_token to this value.", "type": "string"}}, "type": "object"}, "ListWorkforcePoolProviderKeysResponse": {"description": "Response message for ListWorkforcePoolProviderKeys.", "id": "ListWorkforcePoolProviderKeysResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "workforcePoolProviderKeys": {"description": "A list of WorkforcePoolProviderKeys.", "items": {"$ref": "WorkforcePoolProviderKey"}, "type": "array"}}, "type": "object"}, "ListWorkforcePoolProvidersResponse": {"description": "Response message for ListWorkforcePoolProviders.", "id": "ListWorkforcePoolProvidersResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "workforcePoolProviders": {"description": "A list of providers.", "items": {"$ref": "WorkforcePoolProvider"}, "type": "array"}}, "type": "object"}, "ListWorkforcePoolsResponse": {"description": "Response message for ListWorkforcePools.", "id": "ListWorkforcePoolsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "workforcePools": {"description": "A list of pools.", "items": {"$ref": "WorkforcePool"}, "type": "array"}}, "type": "object"}, "ListWorkloadIdentityPoolManagedIdentitiesResponse": {"description": "Response message for ListWorkloadIdentityPoolManagedIdentities.", "id": "ListWorkloadIdentityPoolManagedIdentitiesResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "workloadIdentityPoolManagedIdentities": {"description": "A list of managed identities.", "items": {"$ref": "WorkloadIdentityPoolManagedIdentity"}, "type": "array"}}, "type": "object"}, "ListWorkloadIdentityPoolNamespacesResponse": {"description": "Response message for ListWorkloadIdentityPoolNamespaces.", "id": "ListWorkloadIdentityPoolNamespacesResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "workloadIdentityPoolNamespaces": {"description": "A list of namespaces.", "items": {"$ref": "WorkloadIdentityPoolNamespace"}, "type": "array"}}, "type": "object"}, "ListWorkloadIdentityPoolProviderKeysResponse": {"description": "Response message for ListWorkloadIdentityPoolProviderKeys.", "id": "ListWorkloadIdentityPoolProviderKeysResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "workloadIdentityPoolProviderKeys": {"description": "A list of WorkloadIdentityPoolProviderKey", "items": {"$ref": "WorkloadIdentityPoolProviderKey"}, "type": "array"}}, "type": "object"}, "ListWorkloadIdentityPoolProvidersResponse": {"description": "Response message for ListWorkloadIdentityPoolProviders.", "id": "ListWorkloadIdentityPoolProvidersResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "workloadIdentityPoolProviders": {"description": "A list of providers.", "items": {"$ref": "WorkloadIdentityPoolProvider"}, "type": "array"}}, "type": "object"}, "ListWorkloadIdentityPoolsResponse": {"description": "Response message for ListWorkloadIdentityPools.", "id": "ListWorkloadIdentityPoolsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "workloadIdentityPools": {"description": "A list of pools.", "items": {"$ref": "WorkloadIdentityPool"}, "type": "array"}}, "type": "object"}, "OauthClient": {"description": "Represents an OauthClient. Used to access Google Cloud resources on behalf of a Workforce Identity Federation user by using OAuth 2.0 Protocol to obtain an access token from Google Cloud.", "id": "OauthClient", "properties": {"allowedGrantTypes": {"description": "Required. The list of OAuth grant types is allowed for the OauthClient.", "items": {"enum": ["GRANT_TYPE_UNSPECIFIED", "AUTHORIZATION_CODE_GRANT", "REFRESH_TOKEN_GRANT"], "enumDescriptions": ["Should not be used.", "Authorization code grant.", "Refresh token grant."], "type": "string"}, "type": "array"}, "allowedRedirectUris": {"description": "Required. The list of redirect uris that is allowed to redirect back when authorization process is completed.", "items": {"type": "string"}, "type": "array"}, "allowedScopes": {"description": "Required. The list of scopes that the OauthClient is allowed to request during OAuth flows. The following scopes are supported: * `https://www.googleapis.com/auth/cloud-platform`: See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account. * `openid`: The OAuth client can associate you with your personal information on Google Cloud. * `email`: The OAuth client can read a federated identity's email address. * `groups`: The OAuth client can read a federated identity's groups.", "items": {"type": "string"}, "type": "array"}, "clientId": {"description": "Output only. The system-generated OauthClient id.", "readOnly": true, "type": "string"}, "clientType": {"description": "Immutable. The type of OauthClient. Either public or private. For private clients, the client secret can be managed using the dedicated OauthClientCredential resource.", "enum": ["CLIENT_TYPE_UNSPECIFIED", "PUBLIC_CLIENT", "CONFIDENTIAL_CLIENT"], "enumDescriptions": ["Should not be used.", "Public client has no secret.", "Private client."], "type": "string"}, "description": {"description": "Optional. A user-specified description of the OauthClient. Cannot exceed 256 characters.", "type": "string"}, "disabled": {"description": "Optional. Whether the OauthClient is disabled. You cannot use a disabled OAuth client.", "type": "boolean"}, "displayName": {"description": "Optional. A user-specified display name of the OauthClient. Cannot exceed 32 characters.", "type": "string"}, "expireTime": {"description": "Output only. Time after which the OauthClient will be permanently purged and cannot be recovered.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Immutable. Identifier. The resource name of the OauthClient. Format:`projects/{project}/locations/{location}/oauthClients/{oauth_client}`.", "type": "string"}, "state": {"description": "Output only. The state of the OauthClient.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "DELETED"], "enumDescriptions": ["Default value. This value is unused.", "The OauthClient is active.", "The OauthClient is soft-deleted. Soft-deleted OauthClient is permanently deleted after approximately 30 days unless restored via `UndeleteOauthClient`."], "readOnly": true, "type": "string"}}, "type": "object"}, "OauthClientCredential": {"description": "Represents an OauthClientCredential. Used to authenticate an OauthClient while accessing Google Cloud resources on behalf of a user by using OAuth 2.0 Protocol.", "id": "OauthClientCredential", "properties": {"clientSecret": {"description": "Output only. The system-generated OAuth client secret. The client secret must be stored securely. If the client secret is leaked, you must delete and re-create the client credential. To learn more, see [OAuth client and credential security risks and mitigations](https://cloud.google.com/iam/docs/workforce-oauth-app#security)", "readOnly": true, "type": "string"}, "disabled": {"description": "Optional. Whether the OauthClientCredential is disabled. You cannot use a disabled OauthClientCredential.", "type": "boolean"}, "displayName": {"description": "Optional. A user-specified display name of the OauthClientCredential. Cannot exceed 32 characters.", "type": "string"}, "name": {"description": "Immutable. Identifier. The resource name of the OauthClientCredential. Format: `projects/{project}/locations/{location}/oauthClients/{oauth_client}/credentials/{credential}`", "type": "string"}}, "type": "object"}, "Oidc": {"description": "Represents an OpenId Connect 1.0 identity provider.", "id": "Oidc", "properties": {"allowedAudiences": {"description": "Optional. Acceptable values for the `aud` field (audience) in the OIDC token. Token exchange requests are rejected if the token audience does not match one of the configured values. Each audience may be at most 256 characters. A maximum of 10 audiences may be configured. If this list is empty, the OIDC token audience must be equal to the full canonical resource name of the WorkloadIdentityPoolProvider, with or without the HTTPS prefix. For example: ``` //iam.googleapis.com/projects//locations//workloadIdentityPools//providers/ https://iam.googleapis.com/projects//locations//workloadIdentityPools//providers/ ```", "items": {"type": "string"}, "type": "array"}, "issuerUri": {"description": "Required. The OIDC issuer URL. Must be an HTTPS endpoint. Per OpenID Connect Discovery 1.0 spec, the OIDC issuer URL is used to locate the provider's public keys (via `jwks_uri`) for verifying tokens like the OIDC ID token. These public key types must be 'EC' or 'RSA'.", "type": "string"}, "jwksJson": {"description": "Optional. OIDC JWKs in JSON String format. For details on the definition of a JWK, see https://tools.ietf.org/html/rfc7517. If not set, the `jwks_uri` from the discovery document(fetched from the .well-known path of the `issuer_uri`) will be used. Currently, RSA and EC asymmetric keys are supported. The JWK must use following format and include only the following fields: { \"keys\": [ { \"kty\": \"RSA/EC\", \"alg\": \"\", \"use\": \"sig\", \"kid\": \"\", \"n\": \"\", \"e\": \"\", \"x\": \"\", \"y\": \"\", \"crv\": \"\" } ] }", "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "cancelRequested": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have google.longrunning.Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "statusDetail": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "OwnerService": {"description": "The Google Cloud service that owns this namespace.", "id": "OwnerService", "properties": {"principalSubject": {"description": "Required. The service agent principal subject, e.g. \"serviceAccount:<EMAIL>\".", "type": "string"}}, "type": "object"}, "PatchServiceAccountRequest": {"description": "The service account patch request. You can patch only the `display_name` and `description` fields. You must use the `update_mask` field to specify which of these fields you want to patch. Only the fields specified in the request are guaranteed to be returned in the response. Other fields may be empty in the response.", "id": "PatchServiceAccountRequest", "properties": {"serviceAccount": {"$ref": "ServiceAccount"}, "updateMask": {"format": "google-fieldmask", "type": "string"}}, "type": "object"}, "Permission": {"description": "A permission which can be included by a role.", "id": "Permission", "properties": {"apiDisabled": {"description": "The service API associated with the permission is not enabled.", "type": "boolean"}, "customRolesSupportLevel": {"description": "The current custom role support level.", "enum": ["SUPPORTED", "TESTING", "NOT_SUPPORTED"], "enumDescriptions": ["Default state. Permission is fully supported for custom role use.", "Permission is being tested to check custom role compatibility.", "Permission is not supported for custom role use."], "type": "string"}, "description": {"description": "A brief description of what this Permission is used for.", "type": "string"}, "name": {"description": "The name of this Permission.", "type": "string"}, "onlyInPredefinedRoles": {"deprecated": true, "type": "boolean"}, "primaryPermission": {"description": "The preferred name for this permission. If present, then this permission is an alias of, and equivalent to, the listed primary_permission.", "type": "string"}, "stage": {"description": "The current launch stage of the permission.", "enum": ["ALPHA", "BETA", "GA", "DEPRECATED"], "enumDescriptions": ["The permission is currently in an alpha phase.", "The permission is currently in a beta phase.", "The permission is generally available.", "The permission is being deprecated."], "type": "string"}, "title": {"description": "The title of this Permission.", "type": "string"}}, "type": "object"}, "PermissionDelta": {"description": "A PermissionDelta message to record the added_permissions and removed_permissions inside a role.", "id": "PermissionDelta", "properties": {"addedPermissions": {"description": "Added permissions.", "items": {"type": "string"}, "type": "array"}, "removedPermissions": {"description": "Removed permissions.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "PolicyDelta": {"description": "The difference delta between two policies.", "id": "PolicyDelta", "properties": {"bindingDeltas": {"description": "The delta for Bindings between two policies.", "items": {"$ref": "BindingDelta"}, "type": "array"}}, "type": "object"}, "QueryAuditableServicesRequest": {"description": "A request to get the list of auditable services for a resource.", "id": "QueryAuditableServicesRequest", "properties": {"fullResourceName": {"description": "Required. The full resource name to query from the list of auditable services. The name follows the Google Cloud Platform resource format. For example, a Cloud Platform project with id `my-project` will be named `//cloudresourcemanager.googleapis.com/projects/my-project`.", "type": "string"}}, "type": "object"}, "QueryAuditableServicesResponse": {"description": "A response containing a list of auditable services for a resource.", "id": "QueryAuditableServicesResponse", "properties": {"services": {"description": "The auditable services for a resource.", "items": {"$ref": "AuditableService"}, "type": "array"}}, "type": "object"}, "QueryGrantableRolesRequest": {"description": "The grantable role query request.", "id": "QueryGrantableRolesRequest", "properties": {"fullResourceName": {"description": "Required. Required. The full resource name to query from the list of grantable roles. The name follows the Google Cloud Platform resource format. For example, a Cloud Platform project with id `my-project` will be named `//cloudresourcemanager.googleapis.com/projects/my-project`.", "type": "string"}, "pageSize": {"description": "Optional limit on the number of roles to include in the response. The default is 300, and the maximum is 2,000.", "format": "int32", "type": "integer"}, "pageToken": {"description": "Optional pagination token returned in an earlier QueryGrantableRolesResponse.", "type": "string"}, "view": {"enum": ["BASIC", "FULL"], "enumDescriptions": ["Omits the `included_permissions` field. This is the default value.", "Returns all fields."], "type": "string"}}, "type": "object"}, "QueryGrantableRolesResponse": {"description": "The grantable role query response.", "id": "QueryGrantableRolesResponse", "properties": {"nextPageToken": {"description": "To retrieve the next page of results, set `QueryGrantableRolesRequest.page_token` to this value.", "type": "string"}, "roles": {"description": "The list of matching roles.", "items": {"$ref": "Role"}, "type": "array"}}, "type": "object"}, "QueryTestablePermissionsRequest": {"description": "A request to get permissions which can be tested on a resource.", "id": "QueryTestablePermissionsRequest", "properties": {"fullResourceName": {"description": "Required. The full resource name to query from the list of testable permissions. The name follows the Google Cloud Platform resource format. For example, a Cloud Platform project with id `my-project` will be named `//cloudresourcemanager.googleapis.com/projects/my-project`.", "type": "string"}, "pageSize": {"description": "Optional limit on the number of permissions to include in the response. The default is 100, and the maximum is 1,000.", "format": "int32", "type": "integer"}, "pageToken": {"description": "Optional pagination token returned in an earlier QueryTestablePermissionsRequest.", "type": "string"}}, "type": "object"}, "QueryTestablePermissionsResponse": {"description": "The response containing permissions which can be tested on a resource.", "id": "QueryTestablePermissionsResponse", "properties": {"nextPageToken": {"description": "To retrieve the next page of results, set `QueryTestableRolesRequest.page_token` to this value.", "type": "string"}, "permissions": {"description": "The Permissions testable on the requested resource.", "items": {"$ref": "Permission"}, "type": "array"}}, "type": "object"}, "ReconciliationOperationMetadata": {"description": "Operation metadata returned by the CLH during resource state reconciliation.", "id": "ReconciliationOperationMetadata", "properties": {"deleteResource": {"deprecated": true, "description": "DEPRECATED. Use exclusive_action instead.", "type": "boolean"}, "exclusiveAction": {"description": "Excluisive action returned by the CLH.", "enum": ["UNKNOWN_REPAIR_ACTION", "DELETE", "RETRY"], "enumDeprecated": [false, true, false], "enumDescriptions": ["Unknown repair action.", "The resource has to be deleted. When using this bit, the CLH should fail the operation. DEPRECATED. Instead use DELETE_RESOURCE OperationSignal in SideChannel.", "This resource could not be repaired but the repair should be tried again at a later time. This can happen if there is a dependency that needs to be resolved first- e.g. if a parent resource must be repaired before a child resource."], "type": "string"}}, "type": "object"}, "RemoveAttestationRuleRequest": {"description": "Request message for RemoveAttestationRule.", "id": "RemoveAttestationRuleRequest", "properties": {"attestationRule": {"$ref": "AttestationRule", "description": "Required. The attestation rule to be removed."}}, "type": "object"}, "Role": {"description": "A role in the Identity and Access Management API.", "id": "Role", "properties": {"deleted": {"description": "The current deleted state of the role. This field is read only. It will be ignored in calls to CreateRole and UpdateRole.", "type": "boolean"}, "description": {"description": "Optional. A human-readable description for the role.", "type": "string"}, "etag": {"description": "Used to perform a consistent read-modify-write.", "format": "byte", "type": "string"}, "includedPermissions": {"description": "The names of the permissions this role grants when bound in an IAM policy.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "The name of the role. When `Role` is used in `CreateRole`, the role name must not be set. When `Role` is used in output and other input such as `UpdateRole`, the role name is the complete path. For example, `roles/logging.viewer` for predefined roles, `organizations/{ORGANIZATION_ID}/roles/myRole` for organization-level custom roles, and `projects/{PROJECT_ID}/roles/myRole` for project-level custom roles.", "type": "string"}, "stage": {"description": "The current launch stage of the role. If the `ALPHA` launch stage has been selected for a role, the `stage` field will not be included in the returned definition for the role.", "enum": ["ALPHA", "BETA", "GA", "DEPRECATED", "DISABLED", "EAP"], "enumDescriptions": ["The user has indicated this role is currently in an Alpha phase. If this launch stage is selected, the `stage` field will not be included when requesting the definition for a given role.", "The user has indicated this role is currently in a Beta phase.", "The user has indicated this role is generally available.", "The user has indicated this role is being deprecated.", "This role is disabled and will not contribute permissions to any principals it is granted to in policies.", "The user has indicated this role is currently in an EAP phase."], "type": "string"}, "title": {"description": "Optional. A human-readable title for the role. Typically this is limited to 100 UTF-8 bytes.", "type": "string"}}, "type": "object"}, "Saml": {"description": "Represents an SAML 2.0 identity provider.", "id": "<PERSON><PERSON>", "properties": {"idpMetadataXml": {"description": "Required. SAML identity provider (IdP) configuration metadata XML doc. The XML document must comply with the [SAML 2.0 specification](https://docs.oasis-open.org/security/saml/v2.0/saml-metadata-2.0-os.pdf). The maximum size of an acceptable XML document is 128K characters. The SAML metadata XML document must satisfy the following constraints: * Must contain an IdP Entity ID. * Must contain at least one non-expired signing certificate. * For each signing certificate, the expiration must be: * From no more than 7 days in the future. * To no more than 20 years in the future. * Up to three IdP signing keys are allowed. When updating the provider's metadata XML, at least one non-expired signing key must overlap with the existing metadata. This requirement is skipped if there are no non-expired signing keys present in the existing metadata.", "type": "string"}}, "type": "object"}, "ServiceAccount": {"description": "An IAM service account. A service account is an account for an application or a virtual machine (VM) instance, not a person. You can use a service account to call Google APIs. To learn more, read the [overview of service accounts](https://cloud.google.com/iam/help/service-accounts/overview). When you create a service account, you specify the project ID that owns the service account, as well as a name that must be unique within the project. IAM uses these values to create an email address that identifies the service account. //", "id": "ServiceAccount", "properties": {"description": {"description": "Optional. A user-specified, human-readable description of the service account. The maximum length is 256 UTF-8 bytes.", "type": "string"}, "disabled": {"description": "Output only. Whether the service account is disabled.", "readOnly": true, "type": "boolean"}, "displayName": {"description": "Optional. A user-specified, human-readable name for the service account. The maximum length is 100 UTF-8 bytes.", "type": "string"}, "email": {"description": "Output only. The email address of the service account.", "readOnly": true, "type": "string"}, "etag": {"deprecated": true, "description": "Deprecated. Do not use.", "format": "byte", "type": "string"}, "name": {"description": "The resource name of the service account. Use one of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative, you can use the `-` wildcard character instead of the project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}` * `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the `-` wildcard character, because it can cause response messages to contain misleading error codes. For example, if you try to access the service account `projects/-/serviceAccounts/<EMAIL>`, which does not exist, the response contains an HTTP `403 Forbidden` error instead of a `404 Not Found` error.", "type": "string"}, "oauth2ClientId": {"description": "Output only. The OAuth 2.0 client ID for the service account.", "readOnly": true, "type": "string"}, "projectId": {"description": "Output only. The ID of the project that owns the service account.", "readOnly": true, "type": "string"}, "uniqueId": {"description": "Output only. The unique, stable numeric ID for the service account. Each service account retains its unique ID even if you delete the service account. For example, if you delete a service account, then create a new service account with the same name, the new service account has a different unique ID than the deleted service account.", "readOnly": true, "type": "string"}}, "type": "object"}, "ServiceAccountKey": {"description": "Represents a service account key. A service account has two sets of key-pairs: user-managed, and system-managed. User-managed key-pairs can be created and deleted by users. Users are responsible for rotating these keys periodically to ensure security of their service accounts. Users retain the private key of these key-pairs, and Google retains ONLY the public key. System-managed keys are automatically rotated by Google, and are used for signing for a maximum of two weeks. The rotation process is probabilistic, and usage of the new key will gradually ramp up and down over the key's lifetime. If you cache the public key set for a service account, we recommend that you update the cache every 15 minutes. User-managed keys can be added and removed at any time, so it is important to update the cache frequently. For Google-managed keys, Google will publish a key at least 6 hours before it is first used for signing and will keep publishing it for at least 6 hours after it was last used for signing. Public keys for all service accounts are also published at the OAuth2 Service Account API.", "id": "ServiceAccount<PERSON>ey", "properties": {"disableReason": {"description": "Output only. optional. If the key is disabled, it may have a DisableReason describing why it was disabled.", "enum": ["SERVICE_ACCOUNT_KEY_DISABLE_REASON_UNSPECIFIED", "SERVICE_ACCOUNT_KEY_DISABLE_REASON_USER_INITIATED", "SERVICE_ACCOUNT_KEY_DISABLE_REASON_EXPOSED", "SERVICE_ACCOUNT_KEY_DISABLE_REASON_COMPROMISE_DETECTED"], "enumDescriptions": ["Unspecified disable reason", "Disabled by the user", "Google detected this Service Account external key's private key data as exposed, typically in a public repository on GitHub or similar.", "This service account external key was detected as compromised and used by an attacker."], "readOnly": true, "type": "string"}, "disabled": {"description": "The key status.", "type": "boolean"}, "extendedStatus": {"description": "Output only. Extended Status provides permanent information about a service account key. For example, if this key was detected as exposed or compromised, that information will remain for the lifetime of the key in the extended_status.", "items": {"$ref": "ExtendedStatus"}, "readOnly": true, "type": "array"}, "keyAlgorithm": {"description": "Specifies the algorithm (and possibly key size) for the key.", "enum": ["KEY_ALG_UNSPECIFIED", "KEY_ALG_RSA_1024", "KEY_ALG_RSA_2048"], "enumDescriptions": ["An unspecified key algorithm.", "1k RSA Key.", "2k RSA Key."], "type": "string"}, "keyOrigin": {"description": "The key origin.", "enum": ["ORIGIN_UNSPECIFIED", "USER_PROVIDED", "GOOGLE_PROVIDED"], "enumDescriptions": ["Unspecified key origin.", "Key is provided by user.", "Key is provided by Google."], "type": "string"}, "keyType": {"description": "The key type.", "enum": ["KEY_TYPE_UNSPECIFIED", "USER_MANAGED", "SYSTEM_MANAGED"], "enumDescriptions": ["Unspecified key type. The presence of this in the message will immediately result in an error.", "User-managed keys (managed and rotated by the user).", "System-managed keys (managed and rotated by Google)."], "type": "string"}, "name": {"description": "The resource name of the service account key in the following format `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}/keys/{key}`.", "type": "string"}, "privateKeyData": {"description": "The private key data. Only provided in `CreateServiceAccount<PERSON>ey` responses. Make sure to keep the private key data secure because it allows for the assertion of the service account identity. When base64 decoded, the private key data can be used to authenticate with Google API client libraries and with gcloud auth activate-service-account.", "format": "byte", "type": "string"}, "privateKeyType": {"description": "The output format for the private key. Only provided in `CreateServiceAccountKey` responses, not in `GetServiceAccountKey` or `ListServiceAccountKey` responses. Google never exposes system-managed private keys, and never retains user-managed private keys.", "enum": ["TYPE_UNSPECIFIED", "TYPE_PKCS12_FILE", "TYPE_GOOGLE_CREDENTIALS_FILE"], "enumDescriptions": ["Unspecified. Equivalent to `TYPE_GOOGLE_CREDENTIALS_FILE`.", "PKCS12 format. The password for the PKCS12 file is `notasecret`. For more information, see https://tools.ietf.org/html/rfc7292.", "Google Credentials File format."], "type": "string"}, "publicKeyData": {"description": "The public key data. Only provided in `GetServiceAccountKey` responses.", "format": "byte", "type": "string"}, "validAfterTime": {"description": "The key can be used after this timestamp.", "format": "google-datetime", "type": "string"}, "validBeforeTime": {"description": "The key can be used before this timestamp. For system-managed key pairs, this timestamp is the end time for the private key signing operation. The public key could still be used for verification for a few hours after this time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "ServiceConfig": {"description": "Configuration for a service.", "id": "ServiceConfig", "properties": {"domain": {"description": "Optional. Domain name of the service. Example: console.cloud.google", "type": "string"}}, "type": "object"}, "SetAttestationRulesRequest": {"description": "Request message for SetAttestationRules.", "id": "SetAttestationRulesRequest", "properties": {"attestationRules": {"description": "Required. The attestation rules to be set. At most 50 attestation rules can be set.", "items": {"$ref": "AttestationRule"}, "type": "array"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "SignBlobRequest": {"description": "Deprecated. [Migrate to Service Account Credentials API](https://cloud.google.com/iam/help/credentials/migrate-api). The service account sign blob request.", "id": "SignBlobRequest", "properties": {"bytesToSign": {"deprecated": true, "description": "Required. Deprecated. [Migrate to Service Account Credentials API](https://cloud.google.com/iam/help/credentials/migrate-api). The bytes to sign.", "format": "byte", "type": "string"}}, "type": "object"}, "SignBlobResponse": {"description": "Deprecated. [Migrate to Service Account Credentials API](https://cloud.google.com/iam/help/credentials/migrate-api). The service account sign blob response.", "id": "SignBlobResponse", "properties": {"keyId": {"deprecated": true, "description": "Deprecated. [Migrate to Service Account Credentials API](https://cloud.google.com/iam/help/credentials/migrate-api). The id of the key used to sign the blob.", "type": "string"}, "signature": {"deprecated": true, "description": "Deprecated. [Migrate to Service Account Credentials API](https://cloud.google.com/iam/help/credentials/migrate-api). The signed blob.", "format": "byte", "type": "string"}}, "type": "object"}, "SignJwtRequest": {"description": "Deprecated. [Migrate to Service Account Credentials API](https://cloud.google.com/iam/help/credentials/migrate-api). The service account sign JWT request.", "id": "SignJwtRequest", "properties": {"payload": {"deprecated": true, "description": "Required. Deprecated. [Migrate to Service Account Credentials API](https://cloud.google.com/iam/help/credentials/migrate-api). The JWT payload to sign. Must be a serialized JSON object that contains a JWT Claims Set. For example: `{\"sub\": \"<EMAIL>\", \"iat\": 313435}` If the JWT Claims Set contains an expiration time (`exp`) claim, it must be an integer timestamp that is not in the past and no more than 12 hours in the future. If the JWT Claims Set does not contain an expiration time (`exp`) claim, this claim is added automatically, with a timestamp that is 1 hour in the future.", "type": "string"}}, "type": "object"}, "SignJwtResponse": {"description": "Deprecated. [Migrate to Service Account Credentials API](https://cloud.google.com/iam/help/credentials/migrate-api). The service account sign JWT response.", "id": "SignJwtResponse", "properties": {"keyId": {"deprecated": true, "description": "Deprecated. [Migrate to Service Account Credentials API](https://cloud.google.com/iam/help/credentials/migrate-api). The id of the key used to sign the JWT.", "type": "string"}, "signedJwt": {"deprecated": true, "description": "Deprecated. [Migrate to Service Account Credentials API](https://cloud.google.com/iam/help/credentials/migrate-api). The signed JWT.", "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TrustAnchor": {"description": "Represents a root of trust.", "id": "TrustAnchor", "properties": {"pemCertificate": {"description": "PEM certificate of the PKI used for validation. Must only contain one ca certificate(either root or intermediate cert).", "type": "string"}}, "type": "object"}, "TrustStore": {"description": "Trust store that contains trust anchors and optional intermediate CAs used in PKI to build a trust chain(trust hierarchy) and verify a client's identity.", "id": "TrustStore", "properties": {"intermediateCas": {"description": "Optional. Set of intermediate CA certificates used for building the trust chain to the trust anchor. Important: Intermediate CAs are only supported for X.509 federation.", "items": {"$ref": "IntermediateCA"}, "type": "array"}, "trustAnchors": {"description": "Required. List of trust anchors to be used while performing validation against a given TrustStore. The incoming end entity's certificate must be in the trust chain of one of the trust anchors here.", "items": {"$ref": "TrustAnchor"}, "type": "array"}}, "type": "object"}, "UndeleteOauthClientRequest": {"description": "Request message for UndeleteOauthClient.", "id": "UndeleteOauthClientRequest", "properties": {}, "type": "object"}, "UndeleteRoleRequest": {"description": "The request to undelete an existing role.", "id": "UndeleteRoleRequest", "properties": {"etag": {"description": "Used to perform a consistent read-modify-write.", "format": "byte", "type": "string"}}, "type": "object"}, "UndeleteServiceAccountRequest": {"description": "The service account undelete request.", "id": "UndeleteServiceAccountRequest", "properties": {}, "type": "object"}, "UndeleteServiceAccountResponse": {"id": "UndeleteServiceAccountResponse", "properties": {"restoredAccount": {"$ref": "ServiceAccount", "description": "Metadata for the restored service account."}}, "type": "object"}, "UndeleteWorkforcePoolProviderKeyRequest": {"description": "Request message for UndeleteWorkforcePoolProviderKey.", "id": "UndeleteWorkforcePoolProviderKeyRequest", "properties": {}, "type": "object"}, "UndeleteWorkforcePoolProviderRequest": {"description": "Request message for UndeleteWorkforcePoolProvider.", "id": "UndeleteWorkforcePoolProviderRequest", "properties": {}, "type": "object"}, "UndeleteWorkforcePoolRequest": {"description": "Request message for UndeleteWorkforcePool.", "id": "UndeleteWorkforcePoolRequest", "properties": {}, "type": "object"}, "UndeleteWorkforcePoolSubjectRequest": {"description": "Request message for UndeleteWorkforcePoolSubject.", "id": "UndeleteWorkforcePoolSubjectRequest", "properties": {}, "type": "object"}, "UndeleteWorkloadIdentityPoolManagedIdentityRequest": {"description": "Request message for UndeleteWorkloadIdentityPoolManagedIdentity.", "id": "UndeleteWorkloadIdentityPoolManagedIdentityRequest", "properties": {}, "type": "object"}, "UndeleteWorkloadIdentityPoolNamespaceRequest": {"description": "Request message for UndeleteWorkloadIdentityPoolNamespace.", "id": "UndeleteWorkloadIdentityPoolNamespaceRequest", "properties": {}, "type": "object"}, "UndeleteWorkloadIdentityPoolProviderKeyRequest": {"description": "Request message for UndeleteWorkloadIdentityPoolProviderKey.", "id": "UndeleteWorkloadIdentityPoolProviderKeyRequest", "properties": {}, "type": "object"}, "UndeleteWorkloadIdentityPoolProviderRequest": {"description": "Request message for UndeleteWorkloadIdentityPoolProvider.", "id": "UndeleteWorkloadIdentityPoolProviderRequest", "properties": {}, "type": "object"}, "UndeleteWorkloadIdentityPoolRequest": {"description": "Request message for UndeleteWorkloadIdentityPool.", "id": "UndeleteWorkloadIdentityPoolRequest", "properties": {}, "type": "object"}, "UploadServiceAccountKeyRequest": {"description": "The service account key upload request.", "id": "UploadServiceAccountKeyRequest", "properties": {"publicKeyData": {"description": "The public key to associate with the service account. Must be an RSA public key that is wrapped in an X.509 v3 certificate. Include the first line, `-----BEGIN CERTIFICATE-----`, and the last line, `-----END CERTIFICATE-----`.", "format": "byte", "type": "string"}}, "type": "object"}, "WorkforcePool": {"description": "Represents a collection of external workforces. Provides namespaces for federated users that can be referenced in IAM policies.", "id": "WorkforcePool", "properties": {"accessRestrictions": {"$ref": "AccessRestrictions", "description": "Optional. Configure access restrictions on the workforce pool users. This is an optional field. If specified web sign-in can be restricted to given set of services or programmatic sign-in can be disabled for pool users."}, "description": {"description": "Optional. A user-specified description of the pool. Cannot exceed 256 characters.", "type": "string"}, "disabled": {"description": "Optional. Disables the workforce pool. You cannot use a disabled pool to exchange tokens, or use existing tokens to access resources. If the pool is re-enabled, existing tokens grant access again.", "type": "boolean"}, "displayName": {"description": "Optional. A user-specified display name of the pool in Google Cloud Console. Cannot exceed 32 characters.", "type": "string"}, "expireTime": {"description": "Output only. Time after which the workforce pool will be permanently purged and cannot be recovered.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. The resource name of the pool. Format: `locations/{location}/workforcePools/{workforce_pool_id}`", "type": "string"}, "parent": {"description": "Immutable. The resource name of the parent. Format: `organizations/{org-id}`.", "type": "string"}, "sessionDuration": {"description": "Optional. Duration that the Google Cloud access tokens, console sign-in sessions, and `gcloud` sign-in sessions from this pool are valid. Must be greater than 15 minutes (900s) and less than 12 hours (43200s). If `session_duration` is not configured, minted credentials have a default duration of one hour (3600s). For SAML providers, the lifetime of the token is the minimum of the `session_duration` and the `SessionNotOnOrAfter` claim in the SAML assertion.", "format": "google-duration", "type": "string"}, "state": {"description": "Output only. The state of the pool.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "DELETED"], "enumDescriptions": ["State unspecified.", "The pool is active and may be used in Google Cloud policies.", "The pool is soft-deleted. Soft-deleted pools are permanently deleted after approximately 30 days. You can restore a soft-deleted pool using UndeleteWorkforcePool. You cannot reuse the ID of a soft-deleted pool until it is permanently deleted. While a pool is deleted, you cannot use it to exchange tokens, or use existing tokens to access resources. If the pool is undeleted, existing tokens grant access again."], "readOnly": true, "type": "string"}}, "type": "object"}, "WorkforcePoolProvider": {"description": "A configuration for an external identity provider.", "id": "WorkforcePoolProvider", "properties": {"attributeCondition": {"description": "Optional. A [Common Expression Language](https://opensource.google/projects/cel) expression, in plain text, to restrict what otherwise valid authentication credentials issued by the provider should not be accepted. The expression must output a boolean representing whether to allow the federation. The following keywords may be referenced in the expressions: * `assertion`: JSON representing the authentication credential issued by the provider. * `google`: The Google attributes mapped from the assertion in the `attribute_mappings`. `google.profile_photo`, `google.display_name` and `google.posix_username` are not supported. * `attribute`: The custom attributes mapped from the assertion in the `attribute_mappings`. The maximum length of the attribute condition expression is 4096 characters. If unspecified, all valid authentication credentials will be accepted. The following example shows how to only allow credentials with a mapped `google.groups` value of `admins`: ``` \"'admins' in google.groups\" ```", "type": "string"}, "attributeMapping": {"additionalProperties": {"type": "string"}, "description": "Required. Maps attributes from the authentication credentials issued by an external identity provider to Google Cloud attributes, such as `subject` and `segment`. Each key must be a string specifying the Google Cloud IAM attribute to map to. The following keys are supported: * `google.subject`: The principal IAM is authenticating. You can reference this value in IAM bindings. This is also the subject that appears in Cloud Logging logs. This is a required field and the mapped subject cannot exceed 127 bytes. * `google.groups`: Groups the authenticating user belongs to. You can grant groups access to resources using an IAM `principalSet` binding; access applies to all members of the group. * `google.display_name`: The name of the authenticated user. This is an optional field and the mapped display name cannot exceed 100 bytes. If not set, `google.subject` will be displayed instead. This attribute cannot be referenced in IAM bindings. * `google.profile_photo`: The URL that specifies the authenticated user's thumbnail photo. This is an optional field. When set, the image will be visible as the user's profile picture. If not set, a generic user icon will be displayed instead. This attribute cannot be referenced in IAM bindings. * `google.posix_username`: The Linux username used by OS Login. This is an optional field and the mapped POSIX username cannot exceed 32 characters, The key must match the regex \"^a-zA-Z0-9._{0,31}$\". This attribute cannot be referenced in IAM bindings. You can also provide custom attributes by specifying `attribute.{custom_attribute}`, where {custom_attribute} is the name of the custom attribute to be mapped. You can define a maximum of 50 custom attributes. The maximum length of a mapped attribute key is 100 characters, and the key may only contain the characters [a-z0-9_]. You can reference these attributes in IAM policies to define fine-grained access for a workforce pool to Google Cloud resources. For example: * `google.subject`: `principal://iam.googleapis.com/locations/global/workforcePools/{pool}/subject/{value}` * `google.groups`: `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool}/group/{value}` * `attribute.{custom_attribute}`: `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool}/attribute.{custom_attribute}/{value}` Each value must be a [Common Expression Language] (https://opensource.google/projects/cel) function that maps an identity provider credential to the normalized attribute specified by the corresponding map key. You can use the `assertion` keyword in the expression to access a JSON representation of the authentication credential issued by the provider. The maximum length of an attribute mapping expression is 2048 characters. When evaluated, the total size of all mapped attributes must not exceed 4KB. For OIDC providers, you must supply a custom mapping that includes the `google.subject` attribute. For example, the following maps the `sub` claim of the incoming credential to the `subject` attribute on a Google token: ``` {\"google.subject\": \"assertion.sub\"} ```", "type": "object"}, "description": {"description": "Optional. A user-specified description of the provider. Cannot exceed 256 characters.", "type": "string"}, "detailedAuditLogging": {"description": "Optional. If true, populates additional debug information in Cloud Audit Logs for this provider. Logged attribute mappings and values can be found in `sts.googleapis.com` data access logs. Default value is false.", "type": "boolean"}, "disabled": {"description": "Optional. Disables the workforce pool provider. You cannot use a disabled provider to exchange tokens. However, existing tokens still grant access.", "type": "boolean"}, "displayName": {"description": "Optional. A user-specified display name for the provider. Cannot exceed 32 characters.", "type": "string"}, "expireTime": {"description": "Output only. Time after which the workload pool provider will be permanently purged and cannot be recovered.", "format": "google-datetime", "readOnly": true, "type": "string"}, "extraAttributesOauth2Client": {"$ref": "GoogleIamAdminV1WorkforcePoolProviderExtraAttributesOAuth2Client", "description": "Optional. The configuration for OAuth 2.0 client used to get the additional user attributes. This should be used when users can't get the desired claims in authentication credentials. Currently this configuration is only supported with OIDC protocol."}, "name": {"description": "Identifier. The resource name of the provider. Format: `locations/{location}/workforcePools/{workforce_pool_id}/providers/{provider_id}`", "type": "string"}, "oidc": {"$ref": "GoogleIamAdminV1WorkforcePoolProviderOidc", "description": "An OpenId Connect 1.0 identity provider configuration."}, "saml": {"$ref": "GoogleIamAdminV1WorkforcePoolProviderSaml", "description": "A SAML identity provider configuration."}, "state": {"description": "Output only. The state of the provider.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "DELETED"], "enumDescriptions": ["State unspecified.", "The provider is active and may be used to validate authentication credentials.", "The provider is soft-deleted. Soft-deleted providers are permanently deleted after approximately 30 days. You can restore a soft-deleted provider using UndeleteWorkforcePoolProvider."], "readOnly": true, "type": "string"}}, "type": "object"}, "WorkforcePoolProviderKey": {"description": "Represents a public key configuration for a Workforce Pool Provider. The key can be configured in your identity provider to encrypt SAML assertions. Google holds the corresponding private key, which it uses to decrypt encrypted tokens.", "id": "WorkforcePoolProviderKey", "properties": {"expireTime": {"description": "Output only. The time after which the key will be permanently deleted and cannot be recovered. Note that the key may get purged before this time if the total limit of keys per provider is exceeded.", "format": "google-datetime", "readOnly": true, "type": "string"}, "keyData": {"$ref": "KeyData", "description": "Immutable. Public half of the asymmetric key."}, "name": {"description": "Identifier. The resource name of the key. Format: `locations/{location}/workforcePools/{workforce_pool_id}/providers/{provider_id}/keys/{key_id}`", "type": "string"}, "state": {"description": "Output only. The state of the key.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "DELETED"], "enumDescriptions": ["State unspecified.", "The key is active.", "The key is soft-deleted. Soft-deleted keys are permanently deleted after approximately 30 days. You can restore a soft-deleted key using UndeleteWorkforcePoolProviderKey."], "readOnly": true, "type": "string"}, "use": {"description": "Required. The purpose of the key.", "enum": ["KEY_USE_UNSPECIFIED", "ENCRYPTION"], "enumDescriptions": ["KeyUse unspecified.", "The key is used for encryption."], "type": "string"}}, "type": "object"}, "WorkloadIdentityPool": {"description": "Represents a collection of workload identities. You can define IAM policies to grant these identities access to Google Cloud resources.", "id": "WorkloadIdentityPool", "properties": {"description": {"description": "Optional. A description of the pool. Cannot exceed 256 characters.", "type": "string"}, "disabled": {"description": "Optional. Whether the pool is disabled. You cannot use a disabled pool to exchange tokens, or use existing tokens to access resources. If the pool is re-enabled, existing tokens grant access again.", "type": "boolean"}, "displayName": {"description": "Optional. A display name for the pool. Cannot exceed 32 characters.", "type": "string"}, "expireTime": {"description": "Output only. Time after which the workload identity pool will be permanently purged and cannot be recovered.", "format": "google-datetime", "readOnly": true, "type": "string"}, "inlineCertificateIssuanceConfig": {"$ref": "InlineCertificateIssuanceConfig", "description": "Optional. Defines the Certificate Authority (CA) pool resources and configurations required for issuance and rotation of mTLS workload certificates."}, "inlineTrustConfig": {"$ref": "InlineTrustConfig", "description": "Optional. Represents config to add additional trusted trust domains."}, "mode": {"description": "Immutable. The mode the pool is operating in.", "enum": ["MODE_UNSPECIFIED", "FEDERATION_ONLY", "TRUST_DOMAIN"], "enumDescriptions": ["State unspecified. New pools should not use this mode. Pools with an unspecified mode will operate as if they are in federation-only mode.", "Federation-only mode. Federation-only pools can only be used for federating external workload identities into Google Cloud. Unless otherwise noted, no structure or format constraints are applied to workload identities in a federation-only pool, and you cannot create any resources within the pool besides providers.", "Trust-domain mode. Trust-domain pools can be used to assign identities to Google Cloud workloads. All identities within a trust-domain pool must consist of a single namespace and individual workload identifier. The subject identifier for all identities must conform to the following format: `ns//sa/` WorkloadIdentityPoolProviders cannot be created within trust-domain pools."], "type": "string"}, "name": {"description": "Output only. The resource name of the pool.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The state of the pool.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "DELETED"], "enumDescriptions": ["State unspecified.", "The pool is active, and may be used in Google Cloud policies.", "The pool is soft-deleted. Soft-deleted pools are permanently deleted after approximately 30 days. You can restore a soft-deleted pool using UndeleteWorkloadIdentityPool. You cannot reuse the ID of a soft-deleted pool until it is permanently deleted. While a pool is deleted, you cannot use it to exchange tokens, or use existing tokens to access resources. If the pool is undeleted, existing tokens grant access again."], "readOnly": true, "type": "string"}}, "type": "object"}, "WorkloadIdentityPoolManagedIdentity": {"description": "Represents a managed identity for a workload identity pool namespace.", "id": "WorkloadIdentityPoolManagedIdentity", "properties": {"description": {"description": "A description of the managed identity. Cannot exceed 256 characters.", "type": "string"}, "disabled": {"description": "Whether the managed identity is disabled. If disabled, credentials may no longer be issued for the identity, however existing credentials will still be accepted until they expire.", "type": "boolean"}, "expireTime": {"description": "Output only. Time after which the managed identity will be permanently purged and cannot be recovered.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Output only. The resource name of the managed identity.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The state of the managed identity.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "DELETED"], "enumDescriptions": ["State unspecified.", "The managed identity is active.", "The managed identity is soft-deleted. Soft-deleted managed identities are permanently deleted after approximately 30 days. You can restore a soft-deleted managed identity using UndeleteWorkloadIdentityPoolManagedIdentity. You cannot reuse the ID of a soft-deleted managed identity until it is permanently deleted."], "readOnly": true, "type": "string"}}, "type": "object"}, "WorkloadIdentityPoolNamespace": {"description": "Represents a namespace for a workload identity pool. Namespaces are used to segment identities within the pool.", "id": "WorkloadIdentityPoolNamespace", "properties": {"description": {"description": "A description of the namespace. Cannot exceed 256 characters.", "type": "string"}, "disabled": {"description": "Whether the namespace is disabled. If disabled, credentials may no longer be issued for identities within this namespace, however existing credentials will still be accepted until they expire.", "type": "boolean"}, "expireTime": {"description": "Output only. Time after which the namespace will be permanently purged and cannot be recovered.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Output only. The resource name of the namespace.", "readOnly": true, "type": "string"}, "ownerService": {"$ref": "OwnerService", "description": "Output only. The Google Cloud service that owns this namespace.", "readOnly": true}, "state": {"description": "Output only. The state of the namespace.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "DELETED"], "enumDescriptions": ["State unspecified.", "The namespace is active.", "The namespace is soft-deleted. Soft-deleted namespaces are permanently deleted after approximately 30 days. You can restore a soft-deleted namespace using UndeleteWorkloadIdentityPoolNamespace. You cannot reuse the ID of a soft-deleted namespace until it is permanently deleted."], "readOnly": true, "type": "string"}}, "type": "object"}, "WorkloadIdentityPoolOperationMetadata": {"description": "Metadata for long-running WorkloadIdentityPool operations.", "id": "WorkloadIdentityPoolOperationMetadata", "properties": {}, "type": "object"}, "WorkloadIdentityPoolProvider": {"description": "A configuration for an external identity provider.", "id": "WorkloadIdentityPoolProvider", "properties": {"attributeCondition": {"description": "Optional. [A Common Expression Language](https://opensource.google/projects/cel) expression, in plain text, to restrict what otherwise valid authentication credentials issued by the provider should not be accepted. The expression must output a boolean representing whether to allow the federation. The following keywords may be referenced in the expressions: * `assertion`: JSON representing the authentication credential issued by the provider. * `google`: The Google attributes mapped from the assertion in the `attribute_mappings`. * `attribute`: The custom attributes mapped from the assertion in the `attribute_mappings`. The maximum length of the attribute condition expression is 4096 characters. If unspecified, all valid authentication credential are accepted. The following example shows how to only allow credentials with a mapped `google.groups` value of `admins`: ``` \"'admins' in google.groups\" ```", "type": "string"}, "attributeMapping": {"additionalProperties": {"type": "string"}, "description": "Optional. Maps attributes from authentication credentials issued by an external identity provider to Google Cloud attributes, such as `subject` and `segment`. Each key must be a string specifying the Google Cloud IAM attribute to map to. The following keys are supported: * `google.subject`: The principal IAM is authenticating. You can reference this value in IAM bindings. This is also the subject that appears in Cloud Logging logs. Cannot exceed 127 bytes. * `google.groups`: Groups the external identity belongs to. You can grant groups access to resources using an IAM `principalSet` binding; access applies to all members of the group. You can also provide custom attributes by specifying `attribute.{custom_attribute}`, where `{custom_attribute}` is the name of the custom attribute to be mapped. You can define a maximum of 50 custom attributes. The maximum length of a mapped attribute key is 100 characters, and the key may only contain the characters [a-z0-9_]. You can reference these attributes in IAM policies to define fine-grained access for a workload to Google Cloud resources. For example: * `google.subject`: `principal://iam.googleapis.com/projects/{project}/locations/{location}/workloadIdentityPools/{pool}/subject/{value}` * `google.groups`: `principalSet://iam.googleapis.com/projects/{project}/locations/{location}/workloadIdentityPools/{pool}/group/{value}` * `attribute.{custom_attribute}`: `principalSet://iam.googleapis.com/projects/{project}/locations/{location}/workloadIdentityPools/{pool}/attribute.{custom_attribute}/{value}` Each value must be a [Common Expression Language] (https://opensource.google/projects/cel) function that maps an identity provider credential to the normalized attribute specified by the corresponding map key. You can use the `assertion` keyword in the expression to access a JSON representation of the authentication credential issued by the provider. The maximum length of an attribute mapping expression is 2048 characters. When evaluated, the total size of all mapped attributes must not exceed 8KB. For AWS providers, if no attribute mapping is defined, the following default mapping applies: ``` { \"google.subject\":\"assertion.arn\", \"attribute.aws_role\": \"assertion.arn.contains('assumed-role')\" \" ? assertion.arn.extract('{account_arn}assumed-role/')\" \" + 'assumed-role/'\" \" + assertion.arn.extract('assumed-role/{role_name}/')\" \" : assertion.arn\", } ``` If any custom attribute mappings are defined, they must include a mapping to the `google.subject` attribute. For OIDC providers, you must supply a custom mapping, which must include the `google.subject` attribute. For example, the following maps the `sub` claim of the incoming credential to the `subject` attribute on a Google token: ``` {\"google.subject\": \"assertion.sub\"} ```", "type": "object"}, "aws": {"$ref": "Aws", "description": "An Amazon Web Services identity provider."}, "description": {"description": "Optional. A description for the provider. Cannot exceed 256 characters.", "type": "string"}, "disabled": {"description": "Optional. Whether the provider is disabled. You cannot use a disabled provider to exchange tokens. However, existing tokens still grant access.", "type": "boolean"}, "displayName": {"description": "Optional. A display name for the provider. Cannot exceed 32 characters.", "type": "string"}, "expireTime": {"description": "Output only. Time after which the workload identity pool provider will be permanently purged and cannot be recovered.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Output only. The resource name of the provider.", "readOnly": true, "type": "string"}, "oidc": {"$ref": "Oidc", "description": "An OpenId Connect 1.0 identity provider."}, "saml": {"$ref": "<PERSON><PERSON>", "description": "An SAML 2.0 identity provider."}, "state": {"description": "Output only. The state of the provider.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "DELETED"], "enumDescriptions": ["State unspecified.", "The provider is active, and may be used to validate authentication credentials.", "The provider is soft-deleted. Soft-deleted providers are permanently deleted after approximately 30 days. You can restore a soft-deleted provider using UndeleteWorkloadIdentityPoolProvider. You cannot reuse the ID of a soft-deleted provider until it is permanently deleted."], "readOnly": true, "type": "string"}, "x509": {"$ref": "X509", "description": "An X.509-type identity provider."}}, "type": "object"}, "WorkloadIdentityPoolProviderKey": {"description": "Represents a public key configuration for your workload identity pool provider. The key can be configured in your identity provider to encrypt the SAML assertions. Google holds the corresponding private key which it uses to decrypt encrypted tokens.", "id": "WorkloadIdentityPoolProviderKey", "properties": {"expireTime": {"description": "Output only. Time after which the key will be permanently purged and cannot be recovered. Note that the key may get purged before this timestamp if the total limit of keys per provider is crossed.", "format": "google-datetime", "readOnly": true, "type": "string"}, "keyData": {"$ref": "KeyData", "description": "Immutable. Public half of the asymmetric key."}, "name": {"description": "Output only. The resource name of the key.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The state of the key.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "DELETED"], "enumDescriptions": ["State unspecified.", "The key is active.", "The key is soft-deleted. Soft-deleted keys are permanently deleted after approximately 30 days. You can restore a soft-deleted key using UndeleteWorkloadIdentityPoolProviderKey. While a key is deleted, you cannot use it during the federation."], "readOnly": true, "type": "string"}, "use": {"description": "Required. The purpose of the key.", "enum": ["KEY_USE_UNSPECIFIED", "ENCRYPTION"], "enumDescriptions": ["The key use is not known.", "The public key is used for encryption purposes."], "type": "string"}}, "type": "object"}, "X509": {"description": "An X.509-type identity provider represents a CA. It is trusted to assert a client identity if the client has a certificate that chains up to this CA.", "id": "X509", "properties": {"trustStore": {"$ref": "TrustStore", "description": "Required. A TrustStore. Use this trust store as a wrapper to config the trust anchor and optional intermediate cas to help build the trust chain for the incoming end entity certificate. Follow the X.509 guidelines to define those PEM encoded certs. Only one trust store is currently supported."}}, "type": "object"}}, "servicePath": "", "title": "Identity and Access Management (IAM) API", "version": "v1", "version_module": true}