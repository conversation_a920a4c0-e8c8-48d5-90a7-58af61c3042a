Metadata-Version: 2.3
Name: llama-index-cli
Version: 0.4.2
Summary: llama-index cli
License: MIT
Author: llamaindex
Maintainer: <PERSON>
Maintainer-email: and<PERSON><PERSON>@runllama.ai
Requires-Python: >=3.9,<4.0
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Dist: llama-index-core (>=0.12.0,<0.13.0)
Requires-Dist: llama-index-embeddings-openai (>=0.3.0,<0.4.0)
Requires-Dist: llama-index-llms-openai (>=0.4.0,<0.5.0)
Description-Content-Type: text/markdown

# LlamaIndex CLI

## Installation

```sh
pip install llama-index-cli
```

## Usage

```sh
llamaindex-cli -h

usage: llamaindex-cli [-h] {rag,download-llamapack,download-llamadataset,upgrade,upgrade-file,new-package} ...

LlamaIndex CLI tool.

options:
  -h, --help            show this help message and exit

commands:
  {rag,download-llamapack,download-llamadataset,upgrade,upgrade-file,new-package}
    rag                 Ask a question to a document / a directory of documents.
    download-llamapack  Download a llama-pack
    download-llamadataset
                        Download a llama-dataset
    upgrade             Upgrade a directory containing notebooks or python files.
    upgrade-file        Upgrade a single notebook or python file.
    new-package         Initialize a new llama-index package
```

